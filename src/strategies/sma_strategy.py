"""
Simple Moving Average (SMA) crossover strategy.
"""

import backtrader as bt
from .base_strategy import BaseStrategy


class SMAStrategy(BaseStrategy):
    """Simple Moving Average crossover strategy.
    
    This strategy uses two Simple Moving Averages:
    - Fast SMA (shorter period)
    - Slow SMA (longer period)
    
    Buy when fast SMA crosses above slow SMA
    Sell when fast SMA crosses below slow SMA
    """
    
    params = (
        ('fast_period', 10),
        ('slow_period', 30),
        ('printlog', False),
        ('stop_loss', 0.02),
        ('take_profit', 0.06),
        ('position_size', 0.1),
    )
    
    def init_indicators(self):
        """Initialize SMA indicators."""
        # Create the moving averages
        self.fast_sma = bt.indicators.SimpleMovingAverage(
            self.datas[0], 
            period=self.params.fast_period
        )
        
        self.slow_sma = bt.indicators.SimpleMovingAverage(
            self.datas[0], 
            period=self.params.slow_period
        )
        
        # Create crossover signal
        self.crossover = bt.indicators.CrossOver(self.fast_sma, self.slow_sma)
        
        self.logger.info(f"SMA Strategy initialized with fast_period={self.params.fast_period}, "
                        f"slow_period={self.params.slow_period}")
    
    def next_signal(self):
        """Generate trading signals based on SMA crossover.
        
        Returns:
            1 for buy signal, -1 for sell signal, 0 for no signal
        """
        # Check for stop loss or take profit first
        if self.position:
            if self.check_stop_loss():
                self.log(f'STOP LOSS triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
            
            if self.check_take_profit():
                self.log(f'TAKE PROFIT triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
        
        # SMA crossover signals
        if self.crossover[0] > 0:  # Fast SMA crosses above Slow SMA
            return 1
        elif self.crossover[0] < 0:  # Fast SMA crosses below Slow SMA
            return -1
        
        return 0
    
    def next(self):
        """Override next to add SMA-specific logging."""
        # Log SMA values
        self.log(f'Fast SMA: {self.fast_sma[0]:.2f}, Slow SMA: {self.slow_sma[0]:.2f}')
        
        # Call parent next method
        super().next()


class SMATrendStrategy(BaseStrategy):
    """SMA Trend Following Strategy.
    
    This strategy uses a single SMA to determine trend:
    - Buy when price is above SMA (uptrend)
    - Sell when price is below SMA (downtrend)
    """
    
    params = (
        ('sma_period', 20),
        ('printlog', False),
        ('stop_loss', 0.02),
        ('take_profit', 0.06),
        ('position_size', 0.1),
    )
    
    def init_indicators(self):
        """Initialize SMA indicator."""
        self.sma = bt.indicators.SimpleMovingAverage(
            self.datas[0], 
            period=self.params.sma_period
        )
        
        self.logger.info(f"SMA Trend Strategy initialized with sma_period={self.params.sma_period}")
    
    def next_signal(self):
        """Generate trading signals based on price vs SMA.
        
        Returns:
            1 for buy signal, -1 for sell signal, 0 for no signal
        """
        # Check for stop loss or take profit first
        if self.position:
            if self.check_stop_loss():
                self.log(f'STOP LOSS triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
            
            if self.check_take_profit():
                self.log(f'TAKE PROFIT triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
        
        # Trend following signals
        if not self.position:
            if self.dataclose[0] > self.sma[0]:  # Price above SMA - uptrend
                return 1
        else:
            if self.dataclose[0] < self.sma[0]:  # Price below SMA - downtrend
                return -1
        
        return 0
    
    def next(self):
        """Override next to add SMA-specific logging."""
        # Log SMA value
        self.log(f'SMA: {self.sma[0]:.2f}')
        
        # Call parent next method
        super().next()


class SMAMeanReversionStrategy(BaseStrategy):
    """SMA Mean Reversion Strategy.
    
    This strategy assumes price will revert to the mean (SMA):
    - Buy when price is significantly below SMA
    - Sell when price is significantly above SMA
    """
    
    params = (
        ('sma_period', 20),
        ('deviation_threshold', 0.02),  # 2% deviation from SMA
        ('printlog', False),
        ('stop_loss', 0.02),
        ('take_profit', 0.06),
        ('position_size', 0.1),
    )
    
    def init_indicators(self):
        """Initialize SMA indicator."""
        self.sma = bt.indicators.SimpleMovingAverage(
            self.datas[0], 
            period=self.params.sma_period
        )
        
        self.logger.info(f"SMA Mean Reversion Strategy initialized with "
                        f"sma_period={self.params.sma_period}, "
                        f"deviation_threshold={self.params.deviation_threshold}")
    
    def next_signal(self):
        """Generate trading signals based on mean reversion.
        
        Returns:
            1 for buy signal, -1 for sell signal, 0 for no signal
        """
        # Check for stop loss or take profit first
        if self.position:
            if self.check_stop_loss():
                self.log(f'STOP LOSS triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
            
            if self.check_take_profit():
                self.log(f'TAKE PROFIT triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
        
        # Calculate deviation from SMA
        deviation = (self.dataclose[0] - self.sma[0]) / self.sma[0]
        
        # Mean reversion signals
        if not self.position:
            if deviation < -self.params.deviation_threshold:  # Price significantly below SMA
                return 1
        else:
            if deviation > self.params.deviation_threshold:  # Price significantly above SMA
                return -1
            elif abs(deviation) < self.params.deviation_threshold / 2:  # Close to SMA
                return -1 if self.position.size > 0 else 1
        
        return 0
    
    def next(self):
        """Override next to add deviation logging."""
        # Calculate and log deviation
        deviation = (self.dataclose[0] - self.sma[0]) / self.sma[0]
        self.log(f'SMA: {self.sma[0]:.2f}, Deviation: {deviation:.2%}')
        
        # Call parent next method
        super().next()
