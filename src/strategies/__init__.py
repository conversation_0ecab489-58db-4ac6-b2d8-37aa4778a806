"""
Trading strategies for the backtrader system.
"""

from .base_strategy import BaseStrategy
from .sma_strategy import SMAStrategy
from .rsi_strategy import RSIStrategy
from .macd_strategy import MACDStrategy

# Strategy registry for easy access
STRATEGY_REGISTRY = {
    "BaseStrategy": BaseStrategy,
    "SMAStrategy": SMAStrategy,
    "RSIStrategy": RSIStrategy,
    "MACDStrategy": MACDStrategy,
}

def get_strategy(strategy_name: str):
    """Get strategy class by name.
    
    Args:
        strategy_name: Name of the strategy
        
    Returns:
        Strategy class
        
    Raises:
        ValueError: If strategy not found
    """
    if strategy_name not in STRATEGY_REGISTRY:
        available = ", ".join(STRATEGY_REGISTRY.keys())
        raise ValueError(f"Strategy '{strategy_name}' not found. Available strategies: {available}")
    
    return STRATEGY_REGISTRY[strategy_name]

__all__ = [
    "BaseStrategy",
    "SMAStrategy", 
    "RSIStrategy",
    "MACDStrategy",
    "STRATEGY_REGISTRY",
    "get_strategy",
]
