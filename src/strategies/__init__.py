"""
Trading strategies for the backtrader system.
"""

from .base_strategy import BaseStrategy
from .sma_strategy import SMAStrategy, SMATrendStrategy, SMAMeanReversionStrategy
from .rsi_strategy import RSIStrategy, RSIDivergenceStrategy, RSIMeanReversionStrategy
from .macd_strategy import MACDStrategy, MACDHistogramStrategy, MACDDivergenceStrategy, MACDZeroLineStrategy
from .nvda_ema_strategy import NVDAEMAStrategy, NVDAEMAStrategyConservative, NVDAEMAStrategyAggressive

# Strategy registry for easy access
STRATEGY_REGISTRY = {
    "BaseStrategy": BaseStrategy,
    "SMAStrategy": SMAStrategy,
    "SMATrendStrategy": SMATrendStrategy,
    "SMAMeanReversionStrategy": SMAMeanReversionStrategy,
    "RSIStrategy": RSIStrategy,
    "RSIDivergenceStrategy": RSIDivergenceStrategy,
    "RSIMeanReversionStrategy": RSIMeanReversionStrategy,
    "MACDStrategy": MACDStrategy,
    "MACDHistogramStrategy": MACDH<PERSON>ogramStrategy,
    "MACDDivergenceStrategy": MACDDivergenceStrategy,
    "MACDZeroLineStrategy": MACDZeroLineStrategy,
    "NVDAEMAStrategy": NVDAEMAStrategy,
    "NVDAEMAStrategyConservative": NVDAEMAStrategyConservative,
    "NVDAEMAStrategyAggressive": NVDAEMAStrategyAggressive,
}

def get_strategy(strategy_name: str):
    """Get strategy class by name.

    Args:
        strategy_name: Name of the strategy

    Returns:
        Strategy class

    Raises:
        ValueError: If strategy not found
    """
    if strategy_name not in STRATEGY_REGISTRY:
        available = ", ".join(STRATEGY_REGISTRY.keys())
        raise ValueError(f"Strategy '{strategy_name}' not found. Available strategies: {available}")

    return STRATEGY_REGISTRY[strategy_name]


def list_strategies():
    """List all available strategies.

    Returns:
        List of strategy names
    """
    return list(STRATEGY_REGISTRY.keys())

__all__ = [
    "BaseStrategy",
    "SMAStrategy",
    "SMATrendStrategy",
    "SMAMeanReversionStrategy",
    "RSIStrategy",
    "RSIDivergenceStrategy",
    "RSIMeanReversionStrategy",
    "MACDStrategy",
    "MACDHistogramStrategy",
    "MACDDivergenceStrategy",
    "MACDZeroLineStrategy",
    "NVDAEMAStrategy",
    "NVDAEMAStrategyConservative",
    "NVDAEMAStrategyAggressive",
    "STRATEGY_REGISTRY",
    "get_strategy",
    "list_strategies",
]
