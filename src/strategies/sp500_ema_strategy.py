"""
S&P 500 EMA Multi-Asset Strategy.

This strategy extends the NVDA EMA strategy to work with all S&P 500 stocks,
selecting the best signal from all available tickers for trading.
"""

import backtrader as bt
import numpy as np
from typing import Dict, List, Tuple, Optional
from .base_strategy import BaseStrategy
from ..utils.logger import get_logger

logger = get_logger(__name__)


class SP500EMAStrategy(BaseStrategy):
    """S&P 500 EMA Multi-Asset Strategy.
    
    Strategy Logic:
    1. Calculate EMA signals for all S&P 500 stocks
    2. Score and rank signals by strength and quality
    3. Trade only the highest-scoring signal
    4. Exit when trend changes or better signal appears
    """
    
    params = (
        ('ema_fast', 10),
        ('ema_medium', 20),
        ('ema_slow', 50),
        ('position_size', 0.02),  # 2% of account balance
        ('printlog', True),
        ('stop_loss', 0.05),  # 5% stop loss
        ('take_profit', 0.15),  # 15% take profit
        ('min_volume_ratio', 1.5),  # Minimum volume vs average
        ('trend_strength_threshold', 0.02),  # Minimum trend strength
        ('signal_lookback', 5),  # Days to look back for signal confirmation
        ('max_position_hold_days', 30),  # Maximum days to hold position
    )
    
    def __init__(self):
        # Initialize attributes before calling super().__init__()
        self.data_feeds = {}  # Store data feeds by name
        self.ema_indicators = {}  # Store EMA indicators by ticker
        self.signal_scores = {}  # Store signal scores by ticker
        self.current_ticker = None  # Currently traded ticker
        self.position_entry_date = None
        self.signal_history = {}  # Track signal history for each ticker
        super().__init__()
        
    def init_indicators(self):
        """Initialize EMA indicators for all data feeds."""
        logger.info(f"Initializing indicators for {len(self.datas)} data feeds")
        
        for i, data in enumerate(self.datas):
            # Get ticker name from data feed
            ticker = getattr(data, '_name', f'DATA_{i}')
            self.data_feeds[ticker] = data
            
            # Initialize EMAs for this ticker
            self.ema_indicators[ticker] = {
                'ema_10': bt.indicators.ExponentialMovingAverage(
                    data, period=self.params.ema_fast
                ),
                'ema_20': bt.indicators.ExponentialMovingAverage(
                    data, period=self.params.ema_medium
                ),
                'ema_50': bt.indicators.ExponentialMovingAverage(
                    data, period=self.params.ema_slow
                ),
                'volume_ma': bt.indicators.SimpleMovingAverage(
                    data.volume, period=20
                ),
                'atr': bt.indicators.AverageTrueRange(data, period=14),
                'rsi': bt.indicators.RSI(data, period=14)
            }
            
            # Initialize signal history
            self.signal_history[ticker] = []
        
        logger.info(f"Initialized EMA indicators for {len(self.ema_indicators)} tickers")
    
    def is_uptrend(self, ticker: str) -> bool:
        """Check if ticker is in uptrend."""
        if ticker not in self.ema_indicators:
            return False
        
        emas = self.ema_indicators[ticker]
        return (emas['ema_10'][0] > emas['ema_20'][0] and 
                emas['ema_20'][0] > emas['ema_50'][0])
    
    def is_downtrend(self, ticker: str) -> bool:
        """Check if ticker is in downtrend."""
        if ticker not in self.ema_indicators:
            return False
        
        emas = self.ema_indicators[ticker]
        return (emas['ema_10'][0] < emas['ema_20'][0] and 
                emas['ema_20'][0] < emas['ema_50'][0])
    
    def get_trend_strength(self, ticker: str) -> float:
        """Calculate trend strength based on EMA separation."""
        if ticker not in self.ema_indicators:
            return 0.0
        
        emas = self.ema_indicators[ticker]
        data = self.data_feeds[ticker]
        
        # Calculate EMA separation as percentage of price
        ema_10 = emas['ema_10'][0]
        ema_20 = emas['ema_20'][0]
        ema_50 = emas['ema_50'][0]
        price = data.close[0]
        
        if price <= 0:
            return 0.0
        
        # Trend strength based on EMA separation
        if self.is_uptrend(ticker):
            strength = ((ema_10 - ema_50) / price) * 100
        elif self.is_downtrend(ticker):
            strength = ((ema_50 - ema_10) / price) * 100
        else:
            strength = 0.0
        
        return max(0.0, strength)
    
    def check_volume_confirmation(self, ticker: str) -> bool:
        """Check if current volume confirms the signal."""
        if ticker not in self.ema_indicators:
            return False
        
        data = self.data_feeds[ticker]
        volume_ma = self.ema_indicators[ticker]['volume_ma']
        
        if len(volume_ma) == 0 or volume_ma[0] <= 0:
            return False
        
        volume_ratio = data.volume[0] / volume_ma[0]
        return volume_ratio >= self.params.min_volume_ratio
    
    def check_long_reversal_signal(self, ticker: str) -> bool:
        """Check for long reversal signal for specific ticker."""
        if not self.is_uptrend(ticker):
            return False
        
        data = self.data_feeds[ticker]
        ema_10 = self.ema_indicators[ticker]['ema_10']
        
        # Check reversal pattern
        opened_below = data.open[0] < ema_10[0]
        closed_above = data.close[0] > ema_10[0]
        
        return opened_below and closed_above
    
    def check_short_reversal_signal(self, ticker: str) -> bool:
        """Check for short reversal signal for specific ticker."""
        if not self.is_downtrend(ticker):
            return False
        
        data = self.data_feeds[ticker]
        ema_10 = self.ema_indicators[ticker]['ema_10']
        
        # Check reversal pattern
        opened_above = data.open[0] > ema_10[0]
        closed_below = data.close[0] < ema_10[0]
        
        return opened_above and closed_below
    
    def calculate_signal_score(self, ticker: str, signal_type: str) -> float:
        """Calculate comprehensive signal score for ranking.
        
        Args:
            ticker: Ticker symbol
            signal_type: 'long' or 'short'
            
        Returns:
            Signal score (higher is better)
        """
        if ticker not in self.ema_indicators:
            return 0.0
        
        score = 0.0
        data = self.data_feeds[ticker]
        emas = self.ema_indicators[ticker]
        
        # 1. Trend strength (0-40 points)
        trend_strength = self.get_trend_strength(ticker)
        if trend_strength >= self.params.trend_strength_threshold:
            score += min(40, trend_strength * 10)
        
        # 2. Volume confirmation (0-20 points)
        if self.check_volume_confirmation(ticker):
            score += 20
        
        # 3. RSI confirmation (0-15 points)
        rsi = emas['rsi'][0]
        if signal_type == 'long' and 30 <= rsi <= 70:
            score += 15
        elif signal_type == 'short' and 30 <= rsi <= 70:
            score += 15
        
        # 4. Price momentum (0-15 points)
        if len(data.close) >= 3:
            momentum = (data.close[0] - data.close[-2]) / data.close[-2]
            if signal_type == 'long' and momentum > 0:
                score += min(15, momentum * 100)
            elif signal_type == 'short' and momentum < 0:
                score += min(15, abs(momentum) * 100)
        
        # 5. Volatility adjustment (0-10 points)
        atr = emas['atr'][0]
        if atr > 0 and data.close[0] > 0:
            volatility_ratio = atr / data.close[0]
            # Prefer moderate volatility (not too high, not too low)
            if 0.01 <= volatility_ratio <= 0.05:
                score += 10
            elif 0.005 <= volatility_ratio <= 0.08:
                score += 5
        
        return score
    
    def scan_all_signals(self) -> Dict[str, Dict]:
        """Scan all tickers for signals and return ranked results."""
        signals = {}
        
        for ticker in self.data_feeds.keys():
            try:
                # Check for long signals
                if self.check_long_reversal_signal(ticker):
                    score = self.calculate_signal_score(ticker, 'long')
                    if score > 0:
                        signals[ticker] = {
                            'type': 'long',
                            'score': score,
                            'price': self.data_feeds[ticker].close[0],
                            'trend_strength': self.get_trend_strength(ticker)
                        }
                
                # Check for short signals
                elif self.check_short_reversal_signal(ticker):
                    score = self.calculate_signal_score(ticker, 'short')
                    if score > 0:
                        signals[ticker] = {
                            'type': 'short',
                            'score': score,
                            'price': self.data_feeds[ticker].close[0],
                            'trend_strength': self.get_trend_strength(ticker)
                        }
            
            except Exception as e:
                logger.warning(f"Error scanning {ticker}: {e}")
                continue
        
        return signals
    
    def get_best_signal(self) -> Optional[Tuple[str, Dict]]:
        """Get the highest-scoring signal from all tickers."""
        signals = self.scan_all_signals()
        
        if not signals:
            return None
        
        # Sort by score (descending)
        best_ticker = max(signals.keys(), key=lambda t: signals[t]['score'])
        return best_ticker, signals[best_ticker]
    
    def should_exit_position(self) -> bool:
        """Check if current position should be exited."""
        if not self.position or not self.current_ticker:
            return False
        
        # Check stop loss and take profit
        if self.check_stop_loss() or self.check_take_profit():
            return True
        
        # Check if trend has changed
        if self.position.size > 0 and self.is_downtrend(self.current_ticker):
            self.log(f'Exiting {self.current_ticker} long: trend changed to downtrend')
            return True
        elif self.position.size < 0 and self.is_uptrend(self.current_ticker):
            self.log(f'Exiting {self.current_ticker} short: trend changed to uptrend')
            return True
        
        # Check maximum hold period
        if (self.position_entry_date and 
            len(self.data) - self.position_entry_date >= self.params.max_position_hold_days):
            self.log(f'Exiting {self.current_ticker}: maximum hold period reached')
            return True
        
        # Check if a significantly better signal exists
        best_signal = self.get_best_signal()
        if best_signal:
            best_ticker, signal_info = best_signal
            current_score = self.signal_scores.get(self.current_ticker, 0)
            
            # Exit if new signal is significantly better (20+ points higher)
            if (best_ticker != self.current_ticker and 
                signal_info['score'] > current_score + 20):
                self.log(f'Exiting {self.current_ticker} for better signal in {best_ticker} '
                        f'(score: {signal_info["score"]:.1f} vs {current_score:.1f})')
                return True
        
        return False
    
    def next_signal(self):
        """Generate trading signals based on multi-asset EMA analysis."""
        # Check if we should exit current position
        if self.position and self.should_exit_position():
            return -1 if self.position.size > 0 else 1
        
        # If no position, look for best entry signal
        if not self.position:
            best_signal = self.get_best_signal()
            
            if best_signal:
                ticker, signal_info = best_signal
                signal_type = signal_info['type']
                score = signal_info['score']
                
                # Require minimum score threshold
                if score >= 50:  # Minimum score for entry
                    self.current_ticker = ticker
                    self.signal_scores[ticker] = score
                    self.position_entry_date = len(self.data)
                    
                    self.log(f'SIGNAL: {signal_type.upper()} {ticker} at {signal_info["price"]:.2f} '
                            f'(score: {score:.1f}, trend: {signal_info["trend_strength"]:.2f}%)')
                    
                    return 1 if signal_type == 'long' else -1
        
        return 0
    
    def next(self):
        """Main strategy execution."""
        # Update signal scores for all tickers
        self.signal_scores = {}
        for ticker in self.data_feeds.keys():
            try:
                if self.is_uptrend(ticker):
                    self.signal_scores[ticker] = self.calculate_signal_score(ticker, 'long')
                elif self.is_downtrend(ticker):
                    self.signal_scores[ticker] = self.calculate_signal_score(ticker, 'short')
            except:
                continue
        
        # Log top signals
        if len(self.signal_scores) > 0:
            top_signals = sorted(self.signal_scores.items(), key=lambda x: x[1], reverse=True)[:3]
            top_str = ', '.join([f'{t}:{s:.1f}' for t, s in top_signals if s > 0])
            if top_str:
                self.log(f'Top signals: {top_str}')
        
        # Call parent next method
        super().next()
    
    def calculate_position_size(self):
        """Calculate position size for the selected ticker."""
        if not self.current_ticker:
            return 1
        
        portfolio_value = self.broker.getvalue()
        position_value = portfolio_value * self.params.position_size
        current_price = self.data_feeds[self.current_ticker].close[0]
        shares = int(position_value / current_price)
        return max(shares, 1)


class SP500EMAStrategyConservative(SP500EMAStrategy):
    """Conservative version with tighter risk management."""
    
    params = (
        ('ema_fast', 10),
        ('ema_medium', 20),
        ('ema_slow', 50),
        ('position_size', 0.01),  # 1% position size
        ('stop_loss', 0.03),  # 3% stop loss
        ('take_profit', 0.10),  # 10% take profit
        ('min_volume_ratio', 2.0),  # Higher volume requirement
        ('trend_strength_threshold', 0.03),  # Higher trend strength requirement
    )


class SP500EMAStrategyAggressive(SP500EMAStrategy):
    """Aggressive version with higher risk/reward."""
    
    params = (
        ('ema_fast', 10),
        ('ema_medium', 20),
        ('ema_slow', 50),
        ('position_size', 0.03),  # 3% position size
        ('stop_loss', 0.07),  # 7% stop loss
        ('take_profit', 0.20),  # 20% take profit
        ('min_volume_ratio', 1.2),  # Lower volume requirement
        ('trend_strength_threshold', 0.015),  # Lower trend strength requirement
    )
