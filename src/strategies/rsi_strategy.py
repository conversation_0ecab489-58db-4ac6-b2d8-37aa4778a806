"""
RSI (Relative Strength Index) based trading strategies.
"""

import backtrader as bt
from .base_strategy import BaseStrategy


class RSIStrategy(BaseStrategy):
    """RSI-based trading strategy.
    
    This strategy uses RSI to identify overbought and oversold conditions:
    - Buy when RSI is oversold (below lower threshold)
    - Sell when RSI is overbought (above upper threshold)
    """
    
    params = (
        ('rsi_period', 14),
        ('rsi_upper', 70),
        ('rsi_lower', 30),
        ('printlog', False),
        ('stop_loss', 0.02),
        ('take_profit', 0.06),
        ('position_size', 0.1),
    )
    
    def init_indicators(self):
        """Initialize RSI indicator."""
        self.rsi = bt.indicators.RelativeStrengthIndex(
            self.datas[0],
            period=self.params.rsi_period
        )
        
        self.logger.info(f"RSI Strategy initialized with rsi_period={self.params.rsi_period}, "
                        f"upper={self.params.rsi_upper}, lower={self.params.rsi_lower}")
    
    def next_signal(self):
        """Generate trading signals based on RSI levels.
        
        Returns:
            1 for buy signal, -1 for sell signal, 0 for no signal
        """
        # Check for stop loss or take profit first
        if self.position:
            if self.check_stop_loss():
                self.log(f'STOP LOSS triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
            
            if self.check_take_profit():
                self.log(f'TAKE PROFIT triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
        
        # RSI signals
        if not self.position:
            if self.rsi[0] < self.params.rsi_lower:  # Oversold
                return 1
        else:
            if self.rsi[0] > self.params.rsi_upper:  # Overbought
                return -1
        
        return 0
    
    def next(self):
        """Override next to add RSI-specific logging."""
        # Log RSI value
        self.log(f'RSI: {self.rsi[0]:.2f}')
        
        # Call parent next method
        super().next()


class RSIDivergenceStrategy(BaseStrategy):
    """RSI Divergence Strategy.
    
    This strategy looks for divergences between price and RSI:
    - Bullish divergence: Price makes lower lows, RSI makes higher lows
    - Bearish divergence: Price makes higher highs, RSI makes lower highs
    """
    
    params = (
        ('rsi_period', 14),
        ('lookback_period', 20),
        ('printlog', False),
        ('stop_loss', 0.02),
        ('take_profit', 0.06),
        ('position_size', 0.1),
    )
    
    def init_indicators(self):
        """Initialize RSI indicator."""
        self.rsi = bt.indicators.RelativeStrengthIndex(
            self.datas[0],
            period=self.params.rsi_period
        )
        
        self.logger.info(f"RSI Divergence Strategy initialized with "
                        f"rsi_period={self.params.rsi_period}, "
                        f"lookback_period={self.params.lookback_period}")
    
    def detect_bullish_divergence(self):
        """Detect bullish divergence."""
        if len(self) < self.params.lookback_period:
            return False
        
        # Get recent price and RSI lows
        recent_prices = [self.datalow[-i] for i in range(self.params.lookback_period)]
        recent_rsi = [self.rsi[-i] for i in range(self.params.lookback_period)]
        
        # Find local minima
        price_min_idx = recent_prices.index(min(recent_prices))
        rsi_min_idx = recent_rsi.index(min(recent_rsi))
        
        # Check for divergence (simplified)
        if price_min_idx < len(recent_prices) // 2 and rsi_min_idx > len(recent_rsi) // 2:
            return True
        
        return False
    
    def detect_bearish_divergence(self):
        """Detect bearish divergence."""
        if len(self) < self.params.lookback_period:
            return False
        
        # Get recent price and RSI highs
        recent_prices = [self.datahigh[-i] for i in range(self.params.lookback_period)]
        recent_rsi = [self.rsi[-i] for i in range(self.params.lookback_period)]
        
        # Find local maxima
        price_max_idx = recent_prices.index(max(recent_prices))
        rsi_max_idx = recent_rsi.index(max(recent_rsi))
        
        # Check for divergence (simplified)
        if price_max_idx < len(recent_prices) // 2 and rsi_max_idx > len(recent_rsi) // 2:
            return True
        
        return False
    
    def next_signal(self):
        """Generate trading signals based on RSI divergence.
        
        Returns:
            1 for buy signal, -1 for sell signal, 0 for no signal
        """
        # Check for stop loss or take profit first
        if self.position:
            if self.check_stop_loss():
                self.log(f'STOP LOSS triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
            
            if self.check_take_profit():
                self.log(f'TAKE PROFIT triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
        
        # Divergence signals
        if not self.position:
            if self.detect_bullish_divergence():
                self.log('BULLISH DIVERGENCE detected')
                return 1
        else:
            if self.detect_bearish_divergence():
                self.log('BEARISH DIVERGENCE detected')
                return -1
        
        return 0
    
    def next(self):
        """Override next to add RSI-specific logging."""
        # Log RSI value
        self.log(f'RSI: {self.rsi[0]:.2f}')
        
        # Call parent next method
        super().next()


class RSIMeanReversionStrategy(BaseStrategy):
    """RSI Mean Reversion Strategy.
    
    This strategy assumes RSI will revert to 50 (neutral level):
    - Buy when RSI is below 50 and rising
    - Sell when RSI is above 50 and falling
    """
    
    params = (
        ('rsi_period', 14),
        ('rsi_neutral', 50),
        ('momentum_period', 3),
        ('printlog', False),
        ('stop_loss', 0.02),
        ('take_profit', 0.06),
        ('position_size', 0.1),
    )
    
    def init_indicators(self):
        """Initialize RSI and momentum indicators."""
        self.rsi = bt.indicators.RelativeStrengthIndex(
            self.datas[0],
            period=self.params.rsi_period
        )
        
        # RSI momentum (rate of change)
        self.rsi_momentum = bt.indicators.RateOfChange(
            self.rsi,
            period=self.params.momentum_period
        )
        
        self.logger.info(f"RSI Mean Reversion Strategy initialized with "
                        f"rsi_period={self.params.rsi_period}, "
                        f"momentum_period={self.params.momentum_period}")
    
    def next_signal(self):
        """Generate trading signals based on RSI mean reversion.
        
        Returns:
            1 for buy signal, -1 for sell signal, 0 for no signal
        """
        # Check for stop loss or take profit first
        if self.position:
            if self.check_stop_loss():
                self.log(f'STOP LOSS triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
            
            if self.check_take_profit():
                self.log(f'TAKE PROFIT triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
        
        # Mean reversion signals
        if not self.position:
            # Buy when RSI is below neutral and rising
            if (self.rsi[0] < self.params.rsi_neutral and 
                self.rsi_momentum[0] > 0):
                return 1
        else:
            # Sell when RSI is above neutral and falling
            if (self.rsi[0] > self.params.rsi_neutral and 
                self.rsi_momentum[0] < 0):
                return -1
            # Also sell if RSI reaches extreme levels
            elif self.rsi[0] > 80 or self.rsi[0] < 20:
                return -1
        
        return 0
    
    def next(self):
        """Override next to add RSI-specific logging."""
        # Log RSI value and momentum
        self.log(f'RSI: {self.rsi[0]:.2f}, RSI Momentum: {self.rsi_momentum[0]:.2f}')
        
        # Call parent next method
        super().next()
