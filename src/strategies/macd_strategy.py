"""
MACD (Moving Average Convergence Divergence) based trading strategies.
"""

import backtrader as bt
from .base_strategy import BaseStrategy


class MACDStrategy(BaseStrategy):
    """MACD-based trading strategy.
    
    This strategy uses MACD crossover signals:
    - Buy when MACD line crosses above signal line
    - Sell when MACD line crosses below signal line
    """
    
    params = (
        ('fast_ema', 12),
        ('slow_ema', 26),
        ('signal_ema', 9),
        ('printlog', False),
        ('stop_loss', 0.02),
        ('take_profit', 0.06),
        ('position_size', 0.1),
    )
    
    def init_indicators(self):
        """Initialize MACD indicator."""
        self.macd = bt.indicators.MACD(
            self.datas[0],
            period_me1=self.params.fast_ema,
            period_me2=self.params.slow_ema,
            period_signal=self.params.signal_ema
        )
        
        # MACD crossover signal
        self.macd_crossover = bt.indicators.CrossOver(
            self.macd.macd, 
            self.macd.signal
        )
        
        self.logger.info(f"MACD Strategy initialized with "
                        f"fast_ema={self.params.fast_ema}, "
                        f"slow_ema={self.params.slow_ema}, "
                        f"signal_ema={self.params.signal_ema}")
    
    def next_signal(self):
        """Generate trading signals based on MACD crossover.
        
        Returns:
            1 for buy signal, -1 for sell signal, 0 for no signal
        """
        # Check for stop loss or take profit first
        if self.position:
            if self.check_stop_loss():
                self.log(f'STOP LOSS triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
            
            if self.check_take_profit():
                self.log(f'TAKE PROFIT triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
        
        # MACD crossover signals
        if self.macd_crossover[0] > 0:  # MACD crosses above signal
            return 1
        elif self.macd_crossover[0] < 0:  # MACD crosses below signal
            return -1
        
        return 0
    
    def next(self):
        """Override next to add MACD-specific logging."""
        # Log MACD values
        self.log(f'MACD: {self.macd.macd[0]:.4f}, '
                f'Signal: {self.macd.signal[0]:.4f}, '
                f'Histogram: {self.macd.histo[0]:.4f}')
        
        # Call parent next method
        super().next()


class MACDHistogramStrategy(BaseStrategy):
    """MACD Histogram Strategy.
    
    This strategy uses MACD histogram for signals:
    - Buy when histogram turns positive (momentum increasing)
    - Sell when histogram turns negative (momentum decreasing)
    """
    
    params = (
        ('fast_ema', 12),
        ('slow_ema', 26),
        ('signal_ema', 9),
        ('printlog', False),
        ('stop_loss', 0.02),
        ('take_profit', 0.06),
        ('position_size', 0.1),
    )
    
    def init_indicators(self):
        """Initialize MACD indicator."""
        self.macd = bt.indicators.MACD(
            self.datas[0],
            period_me1=self.params.fast_ema,
            period_me2=self.params.slow_ema,
            period_signal=self.params.signal_ema
        )
        
        self.logger.info(f"MACD Histogram Strategy initialized with "
                        f"fast_ema={self.params.fast_ema}, "
                        f"slow_ema={self.params.slow_ema}, "
                        f"signal_ema={self.params.signal_ema}")
    
    def next_signal(self):
        """Generate trading signals based on MACD histogram.
        
        Returns:
            1 for buy signal, -1 for sell signal, 0 for no signal
        """
        # Check for stop loss or take profit first
        if self.position:
            if self.check_stop_loss():
                self.log(f'STOP LOSS triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
            
            if self.check_take_profit():
                self.log(f'TAKE PROFIT triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
        
        # Histogram signals
        if not self.position:
            # Buy when histogram turns positive
            if self.macd.histo[0] > 0 and self.macd.histo[-1] <= 0:
                return 1
        else:
            # Sell when histogram turns negative
            if self.macd.histo[0] < 0 and self.macd.histo[-1] >= 0:
                return -1
        
        return 0
    
    def next(self):
        """Override next to add MACD-specific logging."""
        # Log MACD values
        self.log(f'MACD: {self.macd.macd[0]:.4f}, '
                f'Signal: {self.macd.signal[0]:.4f}, '
                f'Histogram: {self.macd.histo[0]:.4f}')
        
        # Call parent next method
        super().next()


class MACDDivergenceStrategy(BaseStrategy):
    """MACD Divergence Strategy.
    
    This strategy looks for divergences between price and MACD:
    - Bullish divergence: Price makes lower lows, MACD makes higher lows
    - Bearish divergence: Price makes higher highs, MACD makes lower highs
    """
    
    params = (
        ('fast_ema', 12),
        ('slow_ema', 26),
        ('signal_ema', 9),
        ('lookback_period', 20),
        ('printlog', False),
        ('stop_loss', 0.02),
        ('take_profit', 0.06),
        ('position_size', 0.1),
    )
    
    def init_indicators(self):
        """Initialize MACD indicator."""
        self.macd = bt.indicators.MACD(
            self.datas[0],
            period_me1=self.params.fast_ema,
            period_me2=self.params.slow_ema,
            period_signal=self.params.signal_ema
        )
        
        self.logger.info(f"MACD Divergence Strategy initialized with "
                        f"fast_ema={self.params.fast_ema}, "
                        f"slow_ema={self.params.slow_ema}, "
                        f"signal_ema={self.params.signal_ema}, "
                        f"lookback_period={self.params.lookback_period}")
    
    def detect_bullish_divergence(self):
        """Detect bullish divergence between price and MACD."""
        if len(self) < self.params.lookback_period:
            return False
        
        # Get recent price and MACD lows
        recent_prices = [self.datalow[-i] for i in range(self.params.lookback_period)]
        recent_macd = [self.macd.macd[-i] for i in range(self.params.lookback_period)]
        
        # Find local minima (simplified)
        price_min_idx = recent_prices.index(min(recent_prices))
        macd_min_idx = recent_macd.index(min(recent_macd))
        
        # Check for divergence
        if price_min_idx < len(recent_prices) // 2 and macd_min_idx > len(recent_macd) // 2:
            return True
        
        return False
    
    def detect_bearish_divergence(self):
        """Detect bearish divergence between price and MACD."""
        if len(self) < self.params.lookback_period:
            return False
        
        # Get recent price and MACD highs
        recent_prices = [self.datahigh[-i] for i in range(self.params.lookback_period)]
        recent_macd = [self.macd.macd[-i] for i in range(self.params.lookback_period)]
        
        # Find local maxima (simplified)
        price_max_idx = recent_prices.index(max(recent_prices))
        macd_max_idx = recent_macd.index(max(recent_macd))
        
        # Check for divergence
        if price_max_idx < len(recent_prices) // 2 and macd_max_idx > len(recent_macd) // 2:
            return True
        
        return False
    
    def next_signal(self):
        """Generate trading signals based on MACD divergence.
        
        Returns:
            1 for buy signal, -1 for sell signal, 0 for no signal
        """
        # Check for stop loss or take profit first
        if self.position:
            if self.check_stop_loss():
                self.log(f'STOP LOSS triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
            
            if self.check_take_profit():
                self.log(f'TAKE PROFIT triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
        
        # Divergence signals
        if not self.position:
            if self.detect_bullish_divergence():
                self.log('BULLISH DIVERGENCE detected')
                return 1
        else:
            if self.detect_bearish_divergence():
                self.log('BEARISH DIVERGENCE detected')
                return -1
        
        return 0
    
    def next(self):
        """Override next to add MACD-specific logging."""
        # Log MACD values
        self.log(f'MACD: {self.macd.macd[0]:.4f}, '
                f'Signal: {self.macd.signal[0]:.4f}, '
                f'Histogram: {self.macd.histo[0]:.4f}')
        
        # Call parent next method
        super().next()


class MACDZeroLineStrategy(BaseStrategy):
    """MACD Zero Line Strategy.
    
    This strategy uses MACD zero line crossovers:
    - Buy when MACD crosses above zero line
    - Sell when MACD crosses below zero line
    """
    
    params = (
        ('fast_ema', 12),
        ('slow_ema', 26),
        ('signal_ema', 9),
        ('printlog', False),
        ('stop_loss', 0.02),
        ('take_profit', 0.06),
        ('position_size', 0.1),
    )
    
    def init_indicators(self):
        """Initialize MACD indicator."""
        self.macd = bt.indicators.MACD(
            self.datas[0],
            period_me1=self.params.fast_ema,
            period_me2=self.params.slow_ema,
            period_signal=self.params.signal_ema
        )
        
        # Zero line crossover
        self.zero_crossover = bt.indicators.CrossOver(self.macd.macd, 0)
        
        self.logger.info(f"MACD Zero Line Strategy initialized with "
                        f"fast_ema={self.params.fast_ema}, "
                        f"slow_ema={self.params.slow_ema}, "
                        f"signal_ema={self.params.signal_ema}")
    
    def next_signal(self):
        """Generate trading signals based on MACD zero line crossover.
        
        Returns:
            1 for buy signal, -1 for sell signal, 0 for no signal
        """
        # Check for stop loss or take profit first
        if self.position:
            if self.check_stop_loss():
                self.log(f'STOP LOSS triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
            
            if self.check_take_profit():
                self.log(f'TAKE PROFIT triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
        
        # Zero line crossover signals
        if self.zero_crossover[0] > 0:  # MACD crosses above zero
            return 1
        elif self.zero_crossover[0] < 0:  # MACD crosses below zero
            return -1
        
        return 0
    
    def next(self):
        """Override next to add MACD-specific logging."""
        # Log MACD values
        self.log(f'MACD: {self.macd.macd[0]:.4f}, '
                f'Signal: {self.macd.signal[0]:.4f}, '
                f'Histogram: {self.macd.histo[0]:.4f}')
        
        # Call parent next method
        super().next()
