"""
Enhanced S&P 500 EMA Multi-Asset Strategy with Improved Signal Quality and Risk Management.

Key Improvements:
1. Better signal scoring with market regime awareness
2. Enhanced risk management with dynamic position sizing
3. Improved trend strength calculation
4. Market volatility filters
5. Sector rotation awareness
6. Better exit logic with trailing stops
"""

import backtrader as bt
import numpy as np
from typing import Dict, List, Tuple, Optional
from .base_strategy import BaseStrategy
from ..utils.logger import get_logger

logger = get_logger(__name__)


class SP500EMAStrategyEnhanced(BaseStrategy):
    """Enhanced S&P 500 EMA Multi-Asset Strategy with improved performance."""
    
    params = (
        ('ema_fast', 10),
        ('ema_medium', 20),
        ('ema_slow', 50),
        ('position_size', 0.02),  # 2% of account balance
        ('printlog', True),
        ('stop_loss', 0.05),  # 5% stop loss
        ('take_profit', 0.15),  # 15% take profit
        ('trailing_stop', 0.03),  # 3% trailing stop
        ('min_volume_ratio', 1.5),  # Minimum volume vs average
        ('trend_strength_threshold', 0.015),  # Minimum trend strength
        ('signal_lookback', 5),  # Days to look back for signal confirmation
        ('max_position_hold_days', 20),  # Maximum days to hold position
        ('min_signal_score', 60),  # Minimum score for entry
        ('volatility_filter', True),  # Enable volatility filtering
        ('market_regime_filter', True),  # Enable market regime filtering
        ('sector_rotation', True),  # Enable sector rotation awareness
        ('dynamic_sizing', True),  # Enable dynamic position sizing
    )
    
    def __init__(self):
        # Initialize attributes before calling super().__init__()
        self.data_feeds = {}
        self.ema_indicators = {}
        self.signal_scores = {}
        self.current_ticker = None
        self.position_entry_date = None
        self.position_entry_price = None
        self.highest_price_since_entry = None
        self.signal_history = {}
        self.market_regime = 'neutral'  # bull, bear, neutral
        self.vix_proxy = None  # Market volatility proxy
        super().__init__()
    
    def init_indicators(self):
        """Initialize enhanced indicators for all data feeds."""
        logger.info(f"Initializing enhanced indicators for {len(self.datas)} data feeds")
        
        for i, data in enumerate(self.datas):
            ticker = getattr(data, '_name', f'DATA_{i}')
            self.data_feeds[ticker] = data
            
            # Enhanced indicators
            self.ema_indicators[ticker] = {
                'ema_10': bt.indicators.ExponentialMovingAverage(data, period=self.params.ema_fast),
                'ema_20': bt.indicators.ExponentialMovingAverage(data, period=self.params.ema_medium),
                'ema_50': bt.indicators.ExponentialMovingAverage(data, period=self.params.ema_slow),
                'ema_200': bt.indicators.ExponentialMovingAverage(data, period=200),  # Long-term trend
                'volume_ma': bt.indicators.SimpleMovingAverage(data.volume, period=20),
                'volume_ma_short': bt.indicators.SimpleMovingAverage(data.volume, period=5),
                'atr': bt.indicators.AverageTrueRange(data, period=14),
                'atr_percent': bt.indicators.AverageTrueRange(data, period=14) / data.close * 100,
                'rsi': bt.indicators.RSI(data, period=14),
                'rsi_ma': bt.indicators.SimpleMovingAverage(bt.indicators.RSI(data, period=14), period=3),
                'macd': bt.indicators.MACD(data),
                'bb': bt.indicators.BollingerBands(data, period=20),
                'adx': bt.indicators.DirectionalMovement(data, period=14).adx,
                'momentum': bt.indicators.Momentum(data, period=10),
                'price_ma': bt.indicators.SimpleMovingAverage(data.close, period=20),
            }
            
            self.signal_history[ticker] = []
        
        # Market regime indicators (using SPY as proxy if available)
        spy_data = None
        for ticker, data in self.data_feeds.items():
            if ticker == 'SPY':
                spy_data = data
                break
        
        if spy_data:
            self.market_indicators = {
                'spy_ema_50': bt.indicators.ExponentialMovingAverage(spy_data, period=50),
                'spy_ema_200': bt.indicators.ExponentialMovingAverage(spy_data, period=200),
                'spy_rsi': bt.indicators.RSI(spy_data, period=14),
                'spy_atr_percent': bt.indicators.AverageTrueRange(spy_data, period=14) / spy_data.close * 100,
            }
        
        logger.info(f"Initialized enhanced indicators for {len(self.ema_indicators)} tickers")
    
    def get_market_regime(self) -> str:
        """Determine current market regime."""
        if not hasattr(self, 'market_indicators'):
            return 'neutral'
        
        try:
            spy_ema_50 = self.market_indicators['spy_ema_50'][0]
            spy_ema_200 = self.market_indicators['spy_ema_200'][0]
            spy_rsi = self.market_indicators['spy_rsi'][0]
            spy_atr = self.market_indicators['spy_atr_percent'][0]
            
            # Bull market: 50 EMA > 200 EMA, RSI > 40, low volatility
            if spy_ema_50 > spy_ema_200 and spy_rsi > 40 and spy_atr < 3.0:
                return 'bull'
            # Bear market: 50 EMA < 200 EMA, RSI < 60, high volatility
            elif spy_ema_50 < spy_ema_200 and spy_rsi < 60 and spy_atr > 2.5:
                return 'bear'
            else:
                return 'neutral'
        except:
            return 'neutral'
    
    def get_enhanced_trend_strength(self, ticker: str) -> float:
        """Calculate enhanced trend strength with multiple factors."""
        if ticker not in self.ema_indicators:
            return 0.0
        
        emas = self.ema_indicators[ticker]
        data = self.data_feeds[ticker]
        
        try:
            # EMA alignment strength
            ema_10 = emas['ema_10'][0]
            ema_20 = emas['ema_20'][0]
            ema_50 = emas['ema_50'][0]
            ema_200 = emas['ema_200'][0]
            price = data.close[0]
            
            if price <= 0:
                return 0.0
            
            # Calculate multiple trend strength components
            strength_components = []
            
            # 1. EMA separation strength
            if self.is_uptrend(ticker):
                ema_separation = ((ema_10 - ema_50) / price) * 100
                long_term_alignment = 1.0 if price > ema_200 else 0.5
            elif self.is_downtrend(ticker):
                ema_separation = ((ema_50 - ema_10) / price) * 100
                long_term_alignment = 1.0 if price < ema_200 else 0.5
            else:
                return 0.0
            
            strength_components.append(min(5.0, ema_separation))
            
            # 2. ADX trend strength
            adx = emas['adx'][0] if emas['adx'][0] > 0 else 0
            adx_strength = min(3.0, adx / 10.0)  # Scale ADX to 0-3
            strength_components.append(adx_strength)
            
            # 3. Volume confirmation
            volume_ratio = data.volume[0] / emas['volume_ma'][0] if emas['volume_ma'][0] > 0 else 1
            volume_strength = min(2.0, volume_ratio)
            strength_components.append(volume_strength)
            
            # 4. Momentum strength
            momentum = emas['momentum'][0] / price * 100 if price > 0 else 0
            momentum_strength = min(2.0, abs(momentum))
            strength_components.append(momentum_strength)
            
            # Combine all components
            total_strength = sum(strength_components) * long_term_alignment
            return max(0.0, total_strength)
            
        except Exception as e:
            logger.warning(f"Error calculating trend strength for {ticker}: {e}")
            return 0.0
    
    def calculate_enhanced_signal_score(self, ticker: str, signal_type: str) -> float:
        """Calculate enhanced signal score with improved logic."""
        if ticker not in self.ema_indicators:
            return 0.0
        
        score = 0.0
        data = self.data_feeds[ticker]
        emas = self.ema_indicators[ticker]
        
        try:
            # 1. Enhanced trend strength (0-30 points)
            trend_strength = self.get_enhanced_trend_strength(ticker)
            if trend_strength >= self.params.trend_strength_threshold:
                score += min(30, trend_strength * 3)
            
            # 2. Enhanced volume confirmation (0-20 points)
            volume_ma = emas['volume_ma'][0]
            volume_ma_short = emas['volume_ma_short'][0]
            if volume_ma > 0 and volume_ma_short > 0:
                volume_ratio = data.volume[0] / volume_ma
                volume_trend = volume_ma_short / volume_ma
                if volume_ratio >= self.params.min_volume_ratio and volume_trend > 1.05:
                    score += 20
                elif volume_ratio >= self.params.min_volume_ratio:
                    score += 15
            
            # 3. Improved RSI logic (0-15 points)
            rsi = emas['rsi'][0]
            rsi_ma = emas['rsi_ma'][0]
            if signal_type == 'long':
                # Favor oversold conditions with upward RSI momentum
                if rsi < 40 and rsi > rsi_ma:
                    score += 15
                elif rsi < 50 and rsi > rsi_ma:
                    score += 10
            elif signal_type == 'short':
                # Favor overbought conditions with downward RSI momentum
                if rsi > 60 and rsi < rsi_ma:
                    score += 15
                elif rsi > 50 and rsi < rsi_ma:
                    score += 10
            
            # 4. MACD confirmation (0-15 points)
            macd = emas['macd']
            if signal_type == 'long' and macd.macd[0] > macd.signal[0]:
                score += 15
            elif signal_type == 'short' and macd.macd[0] < macd.signal[0]:
                score += 15
            
            # 5. Bollinger Bands position (0-10 points)
            bb = emas['bb']
            price = data.close[0]
            if signal_type == 'long' and price < bb.bot[0]:  # Near lower band
                score += 10
            elif signal_type == 'short' and price > bb.top[0]:  # Near upper band
                score += 10
            
            # 6. Market regime adjustment (0-10 points)
            if self.params.market_regime_filter:
                regime = self.get_market_regime()
                if (signal_type == 'long' and regime == 'bull') or \
                   (signal_type == 'short' and regime == 'bear'):
                    score += 10
                elif regime == 'neutral':
                    score += 5
            
            # 7. Volatility filter (-20 to +5 points)
            if self.params.volatility_filter:
                atr_percent = emas['atr_percent'][0]
                if 0.5 <= atr_percent <= 4.0:  # Moderate volatility
                    score += 5
                elif atr_percent > 6.0:  # Too volatile
                    score -= 20
            
            return max(0.0, score)
            
        except Exception as e:
            logger.warning(f"Error calculating signal score for {ticker}: {e}")
            return 0.0
    
    def calculate_dynamic_position_size(self, ticker: str) -> float:
        """Calculate dynamic position size based on volatility and signal strength."""
        if not self.params.dynamic_sizing or ticker not in self.ema_indicators:
            return self.params.position_size
        
        try:
            emas = self.ema_indicators[ticker]
            atr_percent = emas['atr_percent'][0]
            signal_score = self.signal_scores.get(ticker, 0)
            
            # Base position size
            base_size = self.params.position_size
            
            # Volatility adjustment (reduce size for high volatility)
            if atr_percent > 4.0:
                vol_adjustment = 0.5
            elif atr_percent > 2.5:
                vol_adjustment = 0.75
            else:
                vol_adjustment = 1.0
            
            # Signal strength adjustment
            if signal_score > 80:
                signal_adjustment = 1.2
            elif signal_score > 70:
                signal_adjustment = 1.1
            elif signal_score < 50:
                signal_adjustment = 0.8
            else:
                signal_adjustment = 1.0
            
            # Market regime adjustment
            regime = self.get_market_regime()
            if regime == 'bear':
                regime_adjustment = 0.7
            elif regime == 'bull':
                regime_adjustment = 1.1
            else:
                regime_adjustment = 1.0
            
            adjusted_size = base_size * vol_adjustment * signal_adjustment * regime_adjustment
            return max(0.005, min(0.05, adjusted_size))  # Cap between 0.5% and 5%
            
        except Exception as e:
            logger.warning(f"Error calculating dynamic position size for {ticker}: {e}")
            return self.params.position_size
    
    def should_exit_position_enhanced(self) -> bool:
        """Enhanced exit logic with trailing stops and better conditions."""
        if not self.position or not self.current_ticker:
            return False
        
        data = self.data_feeds[self.current_ticker]
        current_price = data.close[0]
        
        # Update highest price for trailing stop
        if self.position.size > 0:  # Long position
            if self.highest_price_since_entry is None or current_price > self.highest_price_since_entry:
                self.highest_price_since_entry = current_price
            
            # Trailing stop
            if self.params.trailing_stop > 0:
                trailing_stop_price = self.highest_price_since_entry * (1 - self.params.trailing_stop)
                if current_price <= trailing_stop_price:
                    self.log(f'Trailing stop triggered for {self.current_ticker}: {current_price:.2f} <= {trailing_stop_price:.2f}')
                    return True
        
        elif self.position.size < 0:  # Short position
            if self.highest_price_since_entry is None or current_price < self.highest_price_since_entry:
                self.highest_price_since_entry = current_price
            
            # Trailing stop for short
            if self.params.trailing_stop > 0:
                trailing_stop_price = self.highest_price_since_entry * (1 + self.params.trailing_stop)
                if current_price >= trailing_stop_price:
                    self.log(f'Trailing stop triggered for {self.current_ticker}: {current_price:.2f} >= {trailing_stop_price:.2f}')
                    return True
        
        # Standard stop loss and take profit
        if self.check_stop_loss() or self.check_take_profit():
            return True
        
        # Enhanced trend change detection
        if self.position.size > 0:
            # Exit long if trend weakens significantly
            trend_strength = self.get_enhanced_trend_strength(self.current_ticker)
            if trend_strength < 1.0 or self.is_downtrend(self.current_ticker):
                self.log(f'Exiting {self.current_ticker} long: trend weakened (strength: {trend_strength:.2f})')
                return True
        
        elif self.position.size < 0:
            # Exit short if trend weakens significantly
            trend_strength = self.get_enhanced_trend_strength(self.current_ticker)
            if trend_strength < 1.0 or self.is_uptrend(self.current_ticker):
                self.log(f'Exiting {self.current_ticker} short: trend weakened (strength: {trend_strength:.2f})')
                return True
        
        # Time-based exit with market regime consideration
        hold_days = len(self.data) - self.position_entry_date if self.position_entry_date else 0
        max_hold = self.params.max_position_hold_days
        
        # Extend hold time in strong trends
        regime = self.get_market_regime()
        if regime == 'bull' and self.position.size > 0:
            max_hold *= 1.5
        elif regime == 'bear' and self.position.size < 0:
            max_hold *= 1.5
        
        if hold_days >= max_hold:
            self.log(f'Exiting {self.current_ticker}: maximum hold period reached ({hold_days} days)')
            return True
        
        return False
    
    def next_signal(self):
        """Enhanced signal generation with improved logic."""
        # Update market regime
        self.market_regime = self.get_market_regime()
        
        # Check if we should exit current position
        if self.position and self.should_exit_position_enhanced():
            return -1 if self.position.size > 0 else 1
        
        # If no position, look for best entry signal
        if not self.position:
            signals = self.scan_all_signals()
            
            if signals:
                # Use enhanced scoring
                for ticker in signals:
                    signals[ticker]['score'] = self.calculate_enhanced_signal_score(
                        ticker, signals[ticker]['type']
                    )
                
                # Filter by minimum score
                qualified_signals = {
                    ticker: info for ticker, info in signals.items() 
                    if info['score'] >= self.params.min_signal_score
                }
                
                if qualified_signals:
                    # Get best signal
                    best_ticker = max(qualified_signals.keys(), 
                                    key=lambda t: qualified_signals[t]['score'])
                    signal_info = qualified_signals[best_ticker]
                    
                    self.current_ticker = best_ticker
                    self.signal_scores[best_ticker] = signal_info['score']
                    self.position_entry_date = len(self.data)
                    self.position_entry_price = signal_info['price']
                    self.highest_price_since_entry = signal_info['price']
                    
                    # Calculate dynamic position size
                    dynamic_size = self.calculate_dynamic_position_size(best_ticker)
                    
                    self.log(f'ENHANCED SIGNAL: {signal_info["type"].upper()} {best_ticker} '
                            f'at {signal_info["price"]:.2f} (score: {signal_info["score"]:.1f}, '
                            f'size: {dynamic_size:.1%}, regime: {self.market_regime})')
                    
                    return 1 if signal_info['type'] == 'long' else -1
        
        return 0


class SP500EMAStrategyEnhancedConservative(SP500EMAStrategyEnhanced):
    """Conservative enhanced version with tighter risk management."""

    params = (
        ('ema_fast', 10),
        ('ema_medium', 20),
        ('ema_slow', 50),
        ('position_size', 0.01),  # 1% position size
        ('stop_loss', 0.03),  # 3% stop loss
        ('take_profit', 0.08),  # 8% take profit
        ('trailing_stop', 0.02),  # 2% trailing stop
        ('min_volume_ratio', 2.0),  # Higher volume requirement
        ('trend_strength_threshold', 0.02),  # Higher trend strength requirement
        ('min_signal_score', 70),  # Higher minimum score
        ('max_position_hold_days', 15),  # Shorter hold period
    )


class SP500EMAStrategyEnhancedAggressive(SP500EMAStrategyEnhanced):
    """Aggressive enhanced version with higher risk/reward."""

    params = (
        ('ema_fast', 8),
        ('ema_medium', 18),
        ('ema_slow', 45),
        ('position_size', 0.03),  # 3% position size
        ('stop_loss', 0.07),  # 7% stop loss
        ('take_profit', 0.20),  # 20% take profit
        ('trailing_stop', 0.04),  # 4% trailing stop
        ('min_volume_ratio', 1.2),  # Lower volume requirement
        ('trend_strength_threshold', 0.01),  # Lower trend strength requirement
        ('min_signal_score', 50),  # Lower minimum score
        ('max_position_hold_days', 30),  # Longer hold period
    )
