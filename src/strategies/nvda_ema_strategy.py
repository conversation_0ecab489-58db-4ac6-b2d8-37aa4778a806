"""
NVDA EMA Trend Reversal Strategy.

This strategy uses three EMAs (10, 20, 50) to determine trend direction and 
looks for reversal patterns where price opens below/above the 10 EMA but 
closes on the opposite side.
"""

import backtrader as bt
from .base_strategy import BaseStrategy


class NVDAEMAStrategy(BaseStrategy):
    """NVDA EMA Trend Reversal Strategy.
    
    Strategy Logic:
    1. Trend Determination:
       - Uptrend: 10 EMA > 20 EMA > 50 EMA
       - Downtrend: 10 EMA < 20 EMA < 50 EMA
    
    2. Entry Signals:
       - Long: In uptrend, bar opens below 10 EMA and closes above it
       - Short: In downtrend, bar opens above 10 EMA and closes below it
    
    3. Position Sizing: 2% of account balance
    """
    
    params = (
        ('ema_fast', 10),
        ('ema_medium', 20),
        ('ema_slow', 50),
        ('position_size', 0.02),  # 2% of account balance
        ('printlog', True),
        ('stop_loss', 0.05),  # 5% stop loss
        ('take_profit', 0.15),  # 15% take profit
    )
    
    def init_indicators(self):
        """Initialize EMA indicators."""
        # Create the three EMAs
        self.ema_10 = bt.indicators.ExponentialMovingAverage(
            self.datas[0], 
            period=self.params.ema_fast
        )
        
        self.ema_20 = bt.indicators.ExponentialMovingAverage(
            self.datas[0], 
            period=self.params.ema_medium
        )
        
        self.ema_50 = bt.indicators.ExponentialMovingAverage(
            self.datas[0], 
            period=self.params.ema_slow
        )
        
        self.logger.info(f"NVDA EMA Strategy initialized with periods: "
                        f"Fast={self.params.ema_fast}, "
                        f"Medium={self.params.ema_medium}, "
                        f"Slow={self.params.ema_slow}")
    
    def is_uptrend(self):
        """Check if we are in an uptrend (10 EMA > 20 EMA > 50 EMA)."""
        return (self.ema_10[0] > self.ema_20[0] and 
                self.ema_20[0] > self.ema_50[0])
    
    def is_downtrend(self):
        """Check if we are in a downtrend (10 EMA < 20 EMA < 50 EMA)."""
        return (self.ema_10[0] < self.ema_20[0] and 
                self.ema_20[0] < self.ema_50[0])
    
    def get_trend_status(self):
        """Get current trend status as string."""
        if self.is_uptrend():
            return "UPTREND"
        elif self.is_downtrend():
            return "DOWNTREND"
        else:
            return "SIDEWAYS"
    
    def check_long_reversal_signal(self):
        """Check for long reversal signal.
        
        Returns True if:
        - We are in an uptrend
        - Bar opened below 10 EMA
        - Bar closed above 10 EMA
        """
        if not self.is_uptrend():
            return False
        
        # Check if bar opened below 10 EMA and closed above it
        opened_below = self.dataopen[0] < self.ema_10[0]
        closed_above = self.dataclose[0] > self.ema_10[0]
        
        return opened_below and closed_above
    
    def check_short_reversal_signal(self):
        """Check for short reversal signal.
        
        Returns True if:
        - We are in a downtrend
        - Bar opened above 10 EMA
        - Bar closed below 10 EMA
        """
        if not self.is_downtrend():
            return False
        
        # Check if bar opened above 10 EMA and closed below it
        opened_above = self.dataopen[0] > self.ema_10[0]
        closed_below = self.dataclose[0] < self.ema_10[0]
        
        return opened_above and closed_below
    
    def next_signal(self):
        """Generate trading signals based on EMA trend and reversal logic.
        
        Returns:
            1 for buy signal, -1 for sell signal, 0 for no signal
        """
        # Check for stop loss or take profit first
        if self.position:
            if self.check_stop_loss():
                self.log(f'STOP LOSS triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
            
            if self.check_take_profit():
                self.log(f'TAKE PROFIT triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
        
        # Entry signals
        if not self.position:
            # Check for long signal
            if self.check_long_reversal_signal():
                self.log(f'LONG REVERSAL SIGNAL: Opened below 10 EMA ({self.dataopen[0]:.2f} < {self.ema_10[0]:.2f}), '
                        f'Closed above 10 EMA ({self.dataclose[0]:.2f} > {self.ema_10[0]:.2f})')
                return 1
            
            # Check for short signal
            elif self.check_short_reversal_signal():
                self.log(f'SHORT REVERSAL SIGNAL: Opened above 10 EMA ({self.dataopen[0]:.2f} > {self.ema_10[0]:.2f}), '
                        f'Closed below 10 EMA ({self.dataclose[0]:.2f} < {self.ema_10[0]:.2f})')
                return -1
        
        else:
            # Exit logic for existing positions
            if self.position.size > 0:  # Long position
                # Exit long if trend changes to downtrend
                if self.is_downtrend():
                    self.log('TREND CHANGE: Exiting long position due to downtrend')
                    return -1
            
            elif self.position.size < 0:  # Short position
                # Exit short if trend changes to uptrend
                if self.is_uptrend():
                    self.log('TREND CHANGE: Exiting short position due to uptrend')
                    return 1
        
        return 0
    
    def next(self):
        """Override next to add EMA-specific logging."""
        # Log EMA values and trend status
        trend_status = self.get_trend_status()
        self.log(f'Trend: {trend_status} | '
                f'10 EMA: {self.ema_10[0]:.2f}, '
                f'20 EMA: {self.ema_20[0]:.2f}, '
                f'50 EMA: {self.ema_50[0]:.2f} | '
                f'Open: {self.dataopen[0]:.2f}, '
                f'Close: {self.dataclose[0]:.2f}')
        
        # Call parent next method
        super().next()
    
    def calculate_position_size(self):
        """Override to use 2% of account balance."""
        portfolio_value = self.broker.getvalue()
        position_value = portfolio_value * self.params.position_size
        shares = int(position_value / self.dataclose[0])
        return max(shares, 1)  # At least 1 share


class NVDAEMAStrategyConservative(NVDAEMAStrategy):
    """Conservative version of NVDA EMA Strategy with tighter risk management."""
    
    params = (
        ('ema_fast', 10),
        ('ema_medium', 20),
        ('ema_slow', 50),
        ('position_size', 0.01),  # 1% of account balance
        ('printlog', True),
        ('stop_loss', 0.03),  # 3% stop loss
        ('take_profit', 0.10),  # 10% take profit
    )


class NVDAEMAStrategyAggressive(NVDAEMAStrategy):
    """Aggressive version of NVDA EMA Strategy with higher risk/reward."""
    
    params = (
        ('ema_fast', 10),
        ('ema_medium', 20),
        ('ema_slow', 50),
        ('position_size', 0.03),  # 3% of account balance
        ('printlog', True),
        ('stop_loss', 0.07),  # 7% stop loss
        ('take_profit', 0.20),  # 20% take profit
    )
