"""
Base strategy class for all trading strategies.
"""

import backtrader as bt
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod

from ..utils.logger import get_strategy_logger


class BaseStrategy(bt.Strategy, ABC):
    """Base class for all trading strategies."""
    
    # Default parameters that can be overridden by subclasses
    params = (
        ('printlog', False),
        ('stop_loss', 0.02),  # 2% stop loss
        ('take_profit', 0.06),  # 6% take profit
        ('position_size', 0.1),  # 10% of portfolio
    )
    
    def __init__(self):
        """Initialize the strategy."""
        super().__init__()
        
        # Setup logging
        self.logger = get_strategy_logger(self.__class__.__name__)
        
        # Keep references to data
        self.dataclose = self.datas[0].close
        self.dataopen = self.datas[0].open
        self.datahigh = self.datas[0].high
        self.datalow = self.datas[0].low
        self.datavolume = self.datas[0].volume
        
        # Order tracking
        self.order = None
        self.buyprice = None
        self.buycomm = None
        
        # Performance tracking
        self.trade_count = 0
        self.win_count = 0
        self.loss_count = 0
        
        # Initialize strategy-specific indicators
        self.init_indicators()
        
        self.logger.info(f"Strategy {self.__class__.__name__} initialized")
    
    @abstractmethod
    def init_indicators(self):
        """Initialize strategy-specific indicators.
        
        This method should be implemented by subclasses to define
        the indicators they need.
        """
        pass
    
    @abstractmethod
    def next_signal(self):
        """Generate trading signals.
        
        This method should be implemented by subclasses to define
        their trading logic. Should return:
        - 1 for buy signal
        - -1 for sell signal  
        - 0 for no signal
        """
        pass
    
    def log(self, txt, dt=None, doprint=None):
        """Logging function for this strategy."""
        if doprint is None:
            doprint = self.params.printlog
            
        if doprint:
            dt = dt or self.datas[0].datetime.date(0)
            print(f'{dt.isoformat()}, {txt}')
            
        # Also log to file
        self.logger.info(txt)
    
    def notify_order(self, order):
        """Notification of order status changes."""
        if order.status in [order.Submitted, order.Accepted]:
            # Order submitted/accepted - nothing to do
            return
        
        # Check if an order has been completed
        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(f'BUY EXECUTED, Price: {order.executed.price:.2f}, '
                        f'Cost: {order.executed.value:.2f}, '
                        f'Comm: {order.executed.comm:.2f}')
                self.buyprice = order.executed.price
                self.buycomm = order.executed.comm
            else:  # Sell
                self.log(f'SELL EXECUTED, Price: {order.executed.price:.2f}, '
                        f'Cost: {order.executed.value:.2f}, '
                        f'Comm: {order.executed.comm:.2f}')
            
            self.bar_executed = len(self)
            
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('Order Canceled/Margin/Rejected')
        
        # Reset order
        self.order = None
    
    def notify_trade(self, trade):
        """Notification of trade completion."""
        if not trade.isclosed:
            return
        
        self.trade_count += 1
        
        if trade.pnl > 0:
            self.win_count += 1
        else:
            self.loss_count += 1
        
        self.log(f'OPERATION PROFIT, GROSS {trade.pnl:.2f}, NET {trade.pnlcomm:.2f}')
    
    def next(self):
        """Main strategy logic called for each bar."""
        # Log current close price
        self.log(f'Close: {self.dataclose[0]:.2f}')
        
        # Check if we have a pending order
        if self.order:
            return
        
        # Get trading signal
        signal = self.next_signal()
        
        # Execute trades based on signal
        if signal == 1 and not self.position:
            # Buy signal and not in position
            self.log(f'BUY CREATE, {self.dataclose[0]:.2f}')
            self.order = self.buy(size=self.calculate_position_size())
            
        elif signal == -1 and self.position:
            # Sell signal and in position
            self.log(f'SELL CREATE, {self.dataclose[0]:.2f}')
            self.order = self.sell(size=self.position.size)
    
    def calculate_position_size(self):
        """Calculate position size based on portfolio percentage."""
        portfolio_value = self.broker.getvalue()
        position_value = portfolio_value * self.params.position_size
        shares = int(position_value / self.dataclose[0])
        return max(shares, 1)  # At least 1 share
    
    def check_stop_loss(self):
        """Check if stop loss should be triggered."""
        if not self.position:
            return False
        
        if self.position.size > 0:  # Long position
            current_loss = (self.buyprice - self.dataclose[0]) / self.buyprice
            return current_loss >= self.params.stop_loss
        else:  # Short position
            current_loss = (self.dataclose[0] - self.buyprice) / self.buyprice
            return current_loss >= self.params.stop_loss
    
    def check_take_profit(self):
        """Check if take profit should be triggered."""
        if not self.position:
            return False
        
        if self.position.size > 0:  # Long position
            current_profit = (self.dataclose[0] - self.buyprice) / self.buyprice
            return current_profit >= self.params.take_profit
        else:  # Short position
            current_profit = (self.buyprice - self.dataclose[0]) / self.buyprice
            return current_profit >= self.params.take_profit
    
    def stop(self):
        """Called when the strategy stops (end of data)."""
        win_rate = (self.win_count / self.trade_count * 100) if self.trade_count > 0 else 0
        
        self.log(f'Strategy {self.__class__.__name__} finished')
        self.log(f'Final Portfolio Value: {self.broker.getvalue():.2f}')
        self.log(f'Total Trades: {self.trade_count}')
        self.log(f'Wins: {self.win_count}, Losses: {self.loss_count}')
        self.log(f'Win Rate: {win_rate:.1f}%')
        
        self.logger.info(f"Strategy completed with final value: {self.broker.getvalue():.2f}")
    
    def get_strategy_stats(self) -> Dict[str, Any]:
        """Get strategy statistics.
        
        Returns:
            Dictionary with strategy statistics
        """
        win_rate = (self.win_count / self.trade_count * 100) if self.trade_count > 0 else 0
        
        return {
            'strategy_name': self.__class__.__name__,
            'final_value': self.broker.getvalue(),
            'total_trades': self.trade_count,
            'wins': self.win_count,
            'losses': self.loss_count,
            'win_rate': win_rate,
            'parameters': dict(self.params._getpairs()),
        }
