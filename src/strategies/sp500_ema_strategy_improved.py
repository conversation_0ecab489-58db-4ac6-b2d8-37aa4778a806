"""
Improved S&P 500 EMA Strategy with Critical Fixes.

Key Improvements:
1. Fixed RSI logic for proper signal scoring
2. Added volatility filtering
3. Reduced trade frequency with higher thresholds
4. Enhanced trend strength calculation
5. Better exit logic with trailing stops
"""

import backtrader as bt
import numpy as np
from typing import Dict, List, Tuple, Optional
from .sp500_ema_strategy import SP500EMAStrategy
from ..utils.logger import get_logger

logger = get_logger(__name__)


class SP500EMAStrategyImproved(SP500EMAStrategy):
    """Improved S&P 500 EMA Strategy with critical performance fixes."""
    
    params = (
        ('ema_fast', 10),
        ('ema_medium', 20),
        ('ema_slow', 50),
        ('position_size', 0.02),  # 2% of account balance
        ('printlog', True),
        ('stop_loss', 0.04),  # 4% stop loss
        ('take_profit', 0.12),  # 12% take profit
        ('trailing_stop_pct', 0.03),  # 3% trailing stop
        ('min_volume_ratio', 2.0),  # Higher volume requirement
        ('trend_strength_threshold', 0.025),  # Higher trend strength requirement
        ('signal_lookback', 5),
        ('max_position_hold_days', 25),  # Longer hold period
        ('min_signal_score', 75),  # Much higher minimum score
        ('max_volatility_pct', 4.0),  # Maximum ATR% for trading
        ('better_signal_threshold', 35),  # Higher threshold for switching
        ('max_trades_per_month', 3),  # Limit trade frequency
    )
    
    def __init__(self):
        super().__init__()
        self.position_entry_price = None
        self.highest_price_since_entry = None
        self.lowest_price_since_entry = None
        self.trades_this_month = 0
        self.last_trade_month = None
    
    def calculate_improved_trend_strength(self, ticker: str) -> float:
        """Calculate improved trend strength with multiple factors."""
        if ticker not in self.ema_indicators:
            return 0.0
        
        try:
            emas = self.ema_indicators[ticker]
            data = self.data_feeds[ticker]
            
            ema_10 = emas['ema_10'][0]
            ema_20 = emas['ema_20'][0]
            ema_50 = emas['ema_50'][0]
            price = data.close[0]
            volume = data.volume[0]
            volume_ma = emas['volume_ma'][0]
            
            if price <= 0 or volume_ma <= 0:
                return 0.0
            
            strength_factors = []
            
            # 1. EMA alignment and separation (0-5 points)
            if self.is_uptrend(ticker):
                ema_separation = ((ema_10 - ema_50) / price) * 100
                strength_factors.append(min(5.0, ema_separation))
            elif self.is_downtrend(ticker):
                ema_separation = ((ema_50 - ema_10) / price) * 100
                strength_factors.append(min(5.0, ema_separation))
            else:
                return 0.0
            
            # 2. Volume confirmation (0-3 points)
            volume_ratio = volume / volume_ma
            if volume_ratio >= self.params.min_volume_ratio:
                strength_factors.append(3.0)
            elif volume_ratio >= 1.5:
                strength_factors.append(1.5)
            else:
                strength_factors.append(0.0)
            
            # 3. Price momentum (0-2 points)
            if len(data.close) >= 5:
                momentum = (data.close[0] - data.close[-4]) / data.close[-4] * 100
                strength_factors.append(min(2.0, abs(momentum) / 2.0))
            
            # 4. Trend consistency (0-2 points)
            # Check if EMAs are consistently ordered over last 3 days
            consistency_score = 0
            for i in range(min(3, len(emas['ema_10']))):
                if (self.is_uptrend(ticker) and 
                    emas['ema_10'][-i] > emas['ema_20'][-i] > emas['ema_50'][-i]):
                    consistency_score += 0.67
                elif (self.is_downtrend(ticker) and 
                      emas['ema_10'][-i] < emas['ema_20'][-i] < emas['ema_50'][-i]):
                    consistency_score += 0.67
            strength_factors.append(consistency_score)
            
            return sum(strength_factors)
            
        except Exception as e:
            logger.warning(f"Error calculating trend strength for {ticker}: {e}")
            return 0.0
    
    def calculate_improved_signal_score(self, ticker: str, signal_type: str) -> float:
        """Calculate improved signal score with fixed logic."""
        if ticker not in self.ema_indicators:
            return 0.0
        
        score = 0.0
        data = self.data_feeds[ticker]
        emas = self.ema_indicators[ticker]
        
        try:
            # 1. Enhanced trend strength (0-35 points)
            trend_strength = self.calculate_improved_trend_strength(ticker)
            if trend_strength >= self.params.trend_strength_threshold:
                score += min(35, trend_strength * 3)
            
            # 2. Volume confirmation (0-20 points)
            volume_ratio = data.volume[0] / emas['volume_ma'][0] if emas['volume_ma'][0] > 0 else 0
            if volume_ratio >= self.params.min_volume_ratio:
                score += 20
            elif volume_ratio >= 1.5:
                score += 10
            
            # 3. FIXED RSI logic (0-20 points)
            rsi = emas['rsi'][0]
            if signal_type == 'long':
                if rsi < 30:  # Oversold - best for long
                    score += 20
                elif rsi < 40:  # Moderately oversold
                    score += 15
                elif rsi < 50:  # Slightly oversold
                    score += 10
                # No points for overbought conditions on long signals
            elif signal_type == 'short':
                if rsi > 70:  # Overbought - best for short
                    score += 20
                elif rsi > 60:  # Moderately overbought
                    score += 15
                elif rsi > 50:  # Slightly overbought
                    score += 10
                # No points for oversold conditions on short signals
            
            # 4. Price momentum confirmation (0-15 points)
            if len(data.close) >= 3:
                momentum = (data.close[0] - data.close[-2]) / data.close[-2]
                if signal_type == 'long' and momentum > 0.01:  # Positive momentum for long
                    score += min(15, momentum * 500)
                elif signal_type == 'short' and momentum < -0.01:  # Negative momentum for short
                    score += min(15, abs(momentum) * 500)
            
            # 5. Volatility filter (0-10 points, or -30 penalty)
            atr = emas['atr'][0]
            if atr > 0 and data.close[0] > 0:
                atr_percent = (atr / data.close[0]) * 100
                if atr_percent <= self.params.max_volatility_pct:
                    if 1.0 <= atr_percent <= 3.0:  # Optimal volatility range
                        score += 10
                    elif atr_percent <= 4.0:  # Acceptable volatility
                        score += 5
                else:
                    score -= 30  # Penalty for excessive volatility
            
            return max(0.0, score)
            
        except Exception as e:
            logger.warning(f"Error calculating signal score for {ticker}: {e}")
            return 0.0
    
    def check_trade_frequency_limit(self) -> bool:
        """Check if we've exceeded trade frequency limits."""
        current_date = self.data.datetime.date(0)
        current_month = (current_date.year, current_date.month)
        
        if self.last_trade_month != current_month:
            self.trades_this_month = 0
            self.last_trade_month = current_month
        
        return self.trades_this_month < self.params.max_trades_per_month
    
    def should_exit_position_improved(self) -> bool:
        """Improved exit logic with trailing stops."""
        if not self.position or not self.current_ticker:
            return False
        
        data = self.data_feeds[self.current_ticker]
        current_price = data.close[0]
        
        # Initialize tracking prices
        if self.position_entry_price is None:
            self.position_entry_price = current_price
            self.highest_price_since_entry = current_price
            self.lowest_price_since_entry = current_price
        
        # Update price tracking
        if self.position.size > 0:  # Long position
            if current_price > self.highest_price_since_entry:
                self.highest_price_since_entry = current_price
            
            # Trailing stop for long positions
            trailing_stop_price = self.highest_price_since_entry * (1 - self.params.trailing_stop_pct)
            if current_price <= trailing_stop_price:
                self.log(f'Trailing stop triggered for {self.current_ticker}: {current_price:.2f} <= {trailing_stop_price:.2f}')
                return True
        
        elif self.position.size < 0:  # Short position
            if current_price < self.lowest_price_since_entry:
                self.lowest_price_since_entry = current_price
            
            # Trailing stop for short positions
            trailing_stop_price = self.lowest_price_since_entry * (1 + self.params.trailing_stop_pct)
            if current_price >= trailing_stop_price:
                self.log(f'Trailing stop triggered for {self.current_ticker}: {current_price:.2f} >= {trailing_stop_price:.2f}')
                return True
        
        # Standard stop loss and take profit
        if self.check_stop_loss() or self.check_take_profit():
            return True
        
        # Enhanced trend change detection
        trend_strength = self.calculate_improved_trend_strength(self.current_ticker)
        if trend_strength < 2.0:  # Trend has weakened significantly
            self.log(f'Exiting {self.current_ticker}: trend weakened (strength: {trend_strength:.2f})')
            return True
        
        # Time-based exit
        hold_days = len(self.data) - self.position_entry_date if self.position_entry_date else 0
        if hold_days >= self.params.max_position_hold_days:
            self.log(f'Exiting {self.current_ticker}: maximum hold period reached ({hold_days} days)')
            return True
        
        # Check for significantly better signal (higher threshold)
        best_signal = self.get_best_signal()
        if best_signal:
            best_ticker, signal_info = best_signal
            current_score = self.signal_scores.get(self.current_ticker, 0)
            
            if (best_ticker != self.current_ticker and 
                signal_info['score'] > current_score + self.params.better_signal_threshold):
                self.log(f'Exiting {self.current_ticker} for much better signal in {best_ticker} '
                        f'(score: {signal_info["score"]:.1f} vs {current_score:.1f})')
                return True
        
        return False
    
    def next_signal(self):
        """Improved signal generation with frequency limits."""
        # Check if we should exit current position
        if self.position and self.should_exit_position_improved():
            # Reset tracking variables on exit
            self.position_entry_price = None
            self.highest_price_since_entry = None
            self.lowest_price_since_entry = None
            return -1 if self.position.size > 0 else 1
        
        # If no position, look for best entry signal
        if not self.position:
            # Check trade frequency limit
            if not self.check_trade_frequency_limit():
                return 0
            
            signals = self.scan_all_signals()
            
            if signals:
                # Use improved scoring
                for ticker in signals:
                    signals[ticker]['score'] = self.calculate_improved_signal_score(
                        ticker, signals[ticker]['type']
                    )
                
                # Filter by minimum score (much higher threshold)
                qualified_signals = {
                    ticker: info for ticker, info in signals.items() 
                    if info['score'] >= self.params.min_signal_score
                }
                
                if qualified_signals:
                    # Get best signal
                    best_ticker = max(qualified_signals.keys(), 
                                    key=lambda t: qualified_signals[t]['score'])
                    signal_info = qualified_signals[best_ticker]
                    
                    self.current_ticker = best_ticker
                    self.signal_scores[best_ticker] = signal_info['score']
                    self.position_entry_date = len(self.data)
                    self.trades_this_month += 1
                    
                    self.log(f'IMPROVED SIGNAL: {signal_info["type"].upper()} {best_ticker} '
                            f'at {signal_info["price"]:.2f} (score: {signal_info["score"]:.1f}, '
                            f'trades this month: {self.trades_this_month})')
                    
                    return 1 if signal_info['type'] == 'long' else -1
        
        return 0


class SP500EMAStrategyImprovedConservative(SP500EMAStrategyImproved):
    """Conservative version with even tighter controls."""
    
    params = (
        ('position_size', 0.015),  # 1.5% position size
        ('stop_loss', 0.03),  # 3% stop loss
        ('take_profit', 0.08),  # 8% take profit
        ('trailing_stop_pct', 0.02),  # 2% trailing stop
        ('min_volume_ratio', 2.5),  # Even higher volume requirement
        ('min_signal_score', 85),  # Very high minimum score
        ('max_volatility_pct', 3.0),  # Lower volatility tolerance
        ('max_trades_per_month', 2),  # Fewer trades
    )


class SP500EMAStrategyImprovedAggressive(SP500EMAStrategyImproved):
    """Aggressive version with relaxed constraints."""
    
    params = (
        ('position_size', 0.025),  # 2.5% position size
        ('stop_loss', 0.05),  # 5% stop loss
        ('take_profit', 0.15),  # 15% take profit
        ('trailing_stop_pct', 0.04),  # 4% trailing stop
        ('min_volume_ratio', 1.5),  # Lower volume requirement
        ('min_signal_score', 65),  # Lower minimum score
        ('max_volatility_pct', 5.0),  # Higher volatility tolerance
        ('max_trades_per_month', 4),  # More trades allowed
    )
