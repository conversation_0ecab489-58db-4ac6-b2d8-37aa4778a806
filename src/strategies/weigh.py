import yfinance as yf
import pandas as pd
import bt

# === Strategy Parameters ===
ema_fast = 10
ema_mid = 20
ema_slow = 50
vol_ma_period = 20

# === S&P 100 Tickers ===
tickers = [
    "AAPL", "ABBV", "ABT", "ACN", "ADBE", "ADI", "ADM", "ADP", "ADSK", "AIG", "ALL", "AMAT", "AMGN",
    "AMT", "AMZN", "ANET", "ANTM", "AON", "AOS", "APA", "APD", "APH", "ASML", "AVB", "AVGO", "AXP",
    "AZO", "BA", "BAC", "BDX", "BEN", "BIIB", "BK", "BKNG", "BKR", "BLK", "BMY", "BR", "C", "CAT",
    "CB", "CCI", "CCL", "CDNS", "CE", "CF", "CHD", "CHRW", "CHTR", "CI", "CL", "CLX", "CMA", "CMCSA",
    "CME", "CMG", "CMI", "COF", "COO", "COP", "COST", "CPB", "CPRT", "CRM", "CSCO", "CSX", "CTAS",
    "CTSH", "CTVA", "CVS", "CVX", "DD", "DE", "DFS", "DG", "DGX", "DHI", "DHR", "DIS", "DLR", "DLTR",
    "DOV", "DOW", "DRE", "DUK", "DVA", "DVN", "DXC", "EA", "EBAY", "ECL", "ED", "EFX", "EIX", "EL",
    "EMN", "EMR", "ENPH", "EOG", "EPAM", "EQIX", "EQR", "ES", "ESS"
]

# === Get Historical Data and Signals ===
def get_data(ticker):
    df = yf.download(ticker, start="2019-01-01", end="2024-12-31", auto_adjust=True)
    if isinstance(df.columns, pd.MultiIndex):
        df.columns = df.columns.get_level_values(0)

    df["EMA10"] = df["Close"].ewm(span=ema_fast).mean()
    df["EMA20"] = df["Close"].ewm(span=ema_mid).mean()
    df["EMA50"] = df["Close"].ewm(span=ema_slow).mean()
    df["VolMA"] = df["Volume"].rolling(vol_ma_period).mean()
    df["Signal"] = (df["Close"] > df["EMA10"]) & (df["EMA10"] > df["EMA20"]) & (df["Close"] > df["EMA50"]) & (df["Volume"] > df["VolMA"])
    df["Exit"] = df["Close"] < df["EMA10"]
    df.dropna(inplace=True)
    return df

# === Custom Signal Handler ===
class WeighFromSignal(bt.Algo):
    def __init__(self, signal_series):
        super().__init__()
        self.signal_series = signal_series

    def __call__(self, target):
        date = target.now
        if date not in self.signal_series.index:
            return True
        signal = self.signal_series.loc[date]
        weight = 1.0 if signal == 1 else 0.0
        target.temp['weights'] = {target.universe.columns[0]: weight}
        return True

# === Run Batch Backtest ===
backtests = []
for ticker in tickers:
    try:
        print(f"Running strategy for {ticker}")
        df = get_data(ticker)

        signal_series = pd.Series(0, index=df.index)
        in_position = 0
        for i in range(1, len(df)):
            if in_position == 0 and df["Signal"].iloc[i]:
                signal_series.iloc[i] = 1
                in_position = 1
            elif in_position == 1 and df["Exit"].iloc[i]:
                signal_series.iloc[i] = 0
                in_position = 0
            else:
                signal_series.iloc[i] = signal_series.iloc[i - 1]

        price_df = df[["Close"]].rename(columns={"Close": ticker})
        strat = bt.Strategy(
            name=ticker,
            algos=[
                bt.algos.RunDaily(),
                bt.algos.SelectAll(),
                WeighFromSignal(signal_series),
                bt.algos.Rebalance()
            ]
        )
        backtest = bt.Backtest(strat, price_df)
        backtests.append(backtest)

    except Exception as e:
        print(f"❌ Skipped {ticker} due to error: {e}")

# === Run & Export Results ===
results = bt.run(*backtests)
results.display()

# Save overall summary
stats_df = pd.DataFrame(results.stats).T
stats_df.to_csv("sp100_batch_results.csv")
print("\n✅ S&P 100 results saved to sp100_batch_results.csv")

# === Save Daily Equity Curves ===
equity_df = pd.DataFrame()

for name in results.backtests:
    bt_result = results.get(name)
    equity_df[name] = bt_result.prices  # Fixed: don't index with [name]

equity_df.to_csv("sp100_equity_curves.csv")
print("📈 Equity curves saved to sp100_equity_curves.csv")