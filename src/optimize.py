#!/usr/bin/env python3
"""
Optimization script for the Backtrader Trading System.
"""

import argparse
import sys
from itertools import product
from typing import Dict, Any, List, Tuple

import backtrader as bt
import pandas as pd

from .config import config
from .utils.logger import setup_logger
from .utils.data_utils import DataUtils
from .strategies import get_strategy


def setup_cerebro():
    """Setup and configure Cerebro engine for optimization."""
    cerebro = bt.Cerebro(optreturn=False)
    
    # Set broker configuration
    cerebro.broker.setcash(config.broker.cash)
    cerebro.broker.setcommission(commission=config.broker.commission)
    
    # Add sizer
    cerebro.addsizer(bt.sizers.PercentSizer, percents=10)
    
    return cerebro


def add_analyzers(cerebro):
    """Add analyzers for optimization."""
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')


def generate_parameter_combinations(optimize_params: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Generate all parameter combinations for optimization.
    
    Args:
        optimize_params: Dictionary with parameter ranges
        
    Returns:
        List of parameter combinations
    """
    param_names = list(optimize_params.keys())
    param_ranges = []
    
    for param_name, param_config in optimize_params.items():
        start = param_config.get('start', 1)
        stop = param_config.get('stop', 10)
        step = param_config.get('step', 1)
        
        param_range = list(range(start, stop + 1, step))
        param_ranges.append(param_range)
    
    # Generate all combinations
    combinations = []
    for combo in product(*param_ranges):
        param_dict = dict(zip(param_names, combo))
        combinations.append(param_dict)
    
    return combinations


def extract_result_metrics(result) -> Dict[str, Any]:
    """Extract metrics from optimization result.
    
    Args:
        result: Backtrader strategy result
        
    Returns:
        Dictionary with extracted metrics
    """
    metrics = {}
    
    # Basic info
    metrics['params'] = dict(result.params._getpairs())
    
    # Portfolio value
    metrics['final_value'] = result.broker.getvalue()
    metrics['total_return'] = (metrics['final_value'] - config.broker.cash) / config.broker.cash
    
    # Sharpe Ratio
    if hasattr(result.analyzers, 'sharpe'):
        sharpe_analysis = result.analyzers.sharpe.get_analysis()
        metrics['sharpe_ratio'] = sharpe_analysis.get('sharperatio', 0)
    else:
        metrics['sharpe_ratio'] = 0
    
    # Drawdown
    if hasattr(result.analyzers, 'drawdown'):
        dd_analysis = result.analyzers.drawdown.get_analysis()
        metrics['max_drawdown'] = dd_analysis.get('max', {}).get('drawdown', 0)
        metrics['max_drawdown_duration'] = dd_analysis.get('max', {}).get('len', 0)
    else:
        metrics['max_drawdown'] = 0
        metrics['max_drawdown_duration'] = 0
    
    # Trade Analysis
    if hasattr(result.analyzers, 'trades'):
        trade_analysis = result.analyzers.trades.get_analysis()
        metrics['total_trades'] = trade_analysis.get('total', {}).get('total', 0)
        metrics['won_trades'] = trade_analysis.get('won', {}).get('total', 0)
        metrics['lost_trades'] = trade_analysis.get('lost', {}).get('total', 0)
        
        if metrics['total_trades'] > 0:
            metrics['win_rate'] = metrics['won_trades'] / metrics['total_trades']
        else:
            metrics['win_rate'] = 0
        
        # Profit factor
        if 'pnl' in trade_analysis.get('won', {}):
            avg_win = trade_analysis['won']['pnl'].get('average', 0)
            avg_loss = trade_analysis.get('lost', {}).get('pnl', {}).get('average', 0)
            
            if avg_loss != 0:
                metrics['profit_factor'] = abs(avg_win / avg_loss)
            else:
                metrics['profit_factor'] = float('inf') if avg_win > 0 else 0
        else:
            metrics['profit_factor'] = 0
    else:
        metrics.update({
            'total_trades': 0,
            'won_trades': 0,
            'lost_trades': 0,
            'win_rate': 0,
            'profit_factor': 0
        })
    
    return metrics


def run_optimization(
    strategy_name=None,
    symbols=None,
    start_date=None,
    end_date=None,
    optimize_params=None,
    objective='sharpe_ratio',
    max_cpus=None
):
    """Run parameter optimization.
    
    Args:
        strategy_name: Name of strategy to optimize
        symbols: List of symbols to trade
        start_date: Start date for optimization
        end_date: End date for optimization
        optimize_params: Parameters to optimize
        objective: Optimization objective
        max_cpus: Maximum number of CPUs to use
    """
    # Setup logger
    logger = setup_logger(
        level=config.logging.level,
        log_file=config.logging.file,
        console=config.logging.console
    )
    
    logger.info("Starting optimization...")
    
    # Use config defaults if not specified
    strategy_name = strategy_name or config.strategy.name
    symbols = symbols or config.data.symbols
    start_date = start_date or config.data.start_date
    end_date = end_date or config.data.end_date
    optimize_params = optimize_params or config.optimization.optimize_params
    objective = objective or config.optimization.objective
    max_cpus = max_cpus or config.optimization.max_cpus
    
    # Get strategy class
    try:
        strategy_class = get_strategy(strategy_name)
    except ValueError as e:
        logger.error(f"Strategy error: {e}")
        return None
    
    # Generate parameter combinations
    param_combinations = generate_parameter_combinations(optimize_params)
    logger.info(f"Generated {len(param_combinations)} parameter combinations")
    
    if len(param_combinations) == 0:
        logger.error("No parameter combinations to optimize")
        return None
    
    # Setup Cerebro
    cerebro = setup_cerebro()
    
    # Add strategy with optimization parameters
    param_names = list(optimize_params.keys())
    param_ranges = []
    
    for param_name, param_config in optimize_params.items():
        start = param_config.get('start', 1)
        stop = param_config.get('stop', 10)
        step = param_config.get('step', 1)
        param_ranges.append(range(start, stop + 1, step))
    
    # Use optstrategy for optimization
    cerebro.optstrategy(strategy_class, **dict(zip(param_names, param_ranges)))
    
    # Add analyzers
    add_analyzers(cerebro)
    
    # Get data feeds
    data_utils = DataUtils()
    data_feeds = data_utils.get_data_feeds(symbols=symbols)
    
    if not data_feeds:
        logger.error("No data feeds available")
        return None
    
    # Add data feeds to Cerebro
    for feed in data_feeds:
        cerebro.adddata(feed)
    
    logger.info(f"Starting optimization with {max_cpus} CPUs...")
    logger.info(f"Strategy: {strategy_name}")
    logger.info(f"Symbols: {symbols}")
    logger.info(f"Period: {start_date} to {end_date}")
    logger.info(f"Objective: {objective}")
    
    # Run optimization
    results = cerebro.run(maxcpus=max_cpus)
    
    # Extract and analyze results
    optimization_results = []
    
    for result in results:
        metrics = extract_result_metrics(result[0])  # result is a list with one strategy
        optimization_results.append(metrics)
    
    # Convert to DataFrame for analysis
    results_df = pd.DataFrame(optimization_results)
    
    # Sort by objective
    if objective in results_df.columns:
        ascending = objective in ['max_drawdown', 'max_drawdown_duration']
        results_df = results_df.sort_values(objective, ascending=ascending)
    
    logger.info(f"Optimization completed with {len(results_df)} results")
    
    return results_df


def print_optimization_results(results_df: pd.DataFrame, top_n: int = 10):
    """Print optimization results.
    
    Args:
        results_df: DataFrame with optimization results
        top_n: Number of top results to display
    """
    if results_df.empty:
        print("No optimization results to display")
        return
    
    print("\n" + "="*80)
    print(f"TOP {top_n} OPTIMIZATION RESULTS")
    print("="*80)
    
    # Display top results
    top_results = results_df.head(top_n)
    
    for i, (_, row) in enumerate(top_results.iterrows(), 1):
        print(f"\nRank {i}:")
        print(f"  Parameters: {row['params']}")
        print(f"  Total Return: {row['total_return']:.2%}")
        print(f"  Sharpe Ratio: {row['sharpe_ratio']:.3f}")
        print(f"  Max Drawdown: {row['max_drawdown']:.2%}")
        print(f"  Win Rate: {row['win_rate']:.1%}")
        print(f"  Total Trades: {row['total_trades']}")
        print(f"  Profit Factor: {row['profit_factor']:.2f}")
    
    print("\n" + "="*80)
    
    # Summary statistics
    print("\nSUMMARY STATISTICS:")
    print(f"Total Combinations Tested: {len(results_df)}")
    print(f"Best Total Return: {results_df['total_return'].max():.2%}")
    print(f"Best Sharpe Ratio: {results_df['sharpe_ratio'].max():.3f}")
    print(f"Lowest Max Drawdown: {results_df['max_drawdown'].min():.2%}")
    print(f"Highest Win Rate: {results_df['win_rate'].max():.1%}")
    print("="*80)


def save_optimization_results(results_df: pd.DataFrame, filename: str = "optimization_results.csv"):
    """Save optimization results to CSV file.
    
    Args:
        results_df: DataFrame with optimization results
        filename: Output filename
    """
    try:
        results_df.to_csv(filename, index=False)
        print(f"Optimization results saved to {filename}")
    except Exception as e:
        print(f"Error saving results: {e}")


def main():
    """Main entry point for optimization."""
    parser = argparse.ArgumentParser(description="Backtrader Strategy Optimization")
    
    parser.add_argument(
        '--strategy', 
        type=str, 
        help='Strategy name to optimize'
    )
    
    parser.add_argument(
        '--symbols', 
        nargs='+', 
        help='Symbols to trade'
    )
    
    parser.add_argument(
        '--start-date', 
        type=str, 
        help='Start date (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--end-date', 
        type=str, 
        help='End date (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--objective', 
        type=str, 
        choices=['total_return', 'sharpe_ratio', 'max_drawdown'],
        help='Optimization objective'
    )
    
    parser.add_argument(
        '--max-cpus', 
        type=int, 
        help='Maximum number of CPUs to use'
    )
    
    parser.add_argument(
        '--top-n', 
        type=int, 
        default=10,
        help='Number of top results to display'
    )
    
    parser.add_argument(
        '--save-results', 
        type=str, 
        help='Save results to CSV file'
    )
    
    parser.add_argument(
        '--config', 
        type=str, 
        help='Path to configuration file'
    )
    
    args = parser.parse_args()
    
    # Load custom config if specified
    if args.config:
        from .config import Config
        global config
        config = Config(args.config)
    
    # Run optimization
    try:
        results_df = run_optimization(
            strategy_name=args.strategy,
            symbols=args.symbols,
            start_date=args.start_date,
            end_date=args.end_date,
            objective=args.objective,
            max_cpus=args.max_cpus
        )
        
        if results_df is None or results_df.empty:
            print("Optimization failed or returned no results")
            sys.exit(1)
        
        # Print results
        print_optimization_results(results_df, top_n=args.top_n)
        
        # Save results if requested
        if args.save_results:
            save_optimization_results(results_df, args.save_results)
            
    except KeyboardInterrupt:
        print("\nOptimization interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Error running optimization: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
