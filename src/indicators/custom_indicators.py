"""
Custom indicators for the trading system.
"""

import backtrader as bt
import numpy as np


class BollingerBandsPercent(bt.Indicator):
    """Bollinger Bands Percent (%B) indicator.
    
    %B shows where price is relative to the Bollinger Bands:
    - %B = (Price - Lower Band) / (Upper Band - Lower Band)
    - Values above 1 indicate price is above upper band
    - Values below 0 indicate price is below lower band
    """
    
    lines = ('percent_b',)
    params = (
        ('period', 20),
        ('devfactor', 2.0),
    )
    
    def __init__(self):
        self.bb = bt.indicators.BollingerBands(
            self.data,
            period=self.params.period,
            devfactor=self.params.devfactor
        )
        
        # Calculate %B
        self.lines.percent_b = (
            (self.data.close - self.bb.lines.bot) / 
            (self.bb.lines.top - self.bb.lines.bot)
        )


class StochasticRSI(bt.Indicator):
    """Stochastic RSI indicator.
    
    Applies Stochastic oscillator formula to RSI values:
    - StochRSI = (RSI - Lowest Low RSI) / (Highest High RSI - Lowest Low RSI)
    """
    
    lines = ('stochrsi', 'stochrsi_k', 'stochrsi_d')
    params = (
        ('rsi_period', 14),
        ('stoch_period', 14),
        ('k_period', 3),
        ('d_period', 3),
    )
    
    def __init__(self):
        # Calculate RSI
        self.rsi = bt.indicators.RSI(self.data, period=self.params.rsi_period)
        
        # Apply Stochastic to RSI
        self.highest_rsi = bt.indicators.Highest(self.rsi, period=self.params.stoch_period)
        self.lowest_rsi = bt.indicators.Lowest(self.rsi, period=self.params.stoch_period)
        
        # Calculate Stochastic RSI
        self.lines.stochrsi = (
            (self.rsi - self.lowest_rsi) / 
            (self.highest_rsi - self.lowest_rsi) * 100
        )
        
        # Calculate %K and %D
        self.lines.stochrsi_k = bt.indicators.SMA(
            self.lines.stochrsi, 
            period=self.params.k_period
        )
        self.lines.stochrsi_d = bt.indicators.SMA(
            self.lines.stochrsi_k, 
            period=self.params.d_period
        )


class WilliamsR(bt.Indicator):
    """Williams %R indicator.
    
    Momentum oscillator that measures overbought/oversold levels:
    - %R = (Highest High - Close) / (Highest High - Lowest Low) * -100
    """
    
    lines = ('williams_r',)
    params = (('period', 14),)
    
    def __init__(self):
        self.highest = bt.indicators.Highest(self.data.high, period=self.params.period)
        self.lowest = bt.indicators.Lowest(self.data.low, period=self.params.period)
        
        self.lines.williams_r = (
            (self.highest - self.data.close) / 
            (self.highest - self.lowest) * -100
        )


class CommodityChannelIndex(bt.Indicator):
    """Commodity Channel Index (CCI) indicator.
    
    Momentum oscillator that identifies cyclical trends:
    - CCI = (Typical Price - SMA) / (0.015 * Mean Deviation)
    """
    
    lines = ('cci',)
    params = (('period', 20),)
    
    def __init__(self):
        # Calculate Typical Price
        self.typical_price = (self.data.high + self.data.low + self.data.close) / 3
        
        # Calculate SMA of Typical Price
        self.sma_tp = bt.indicators.SMA(self.typical_price, period=self.params.period)
        
        # Calculate Mean Deviation
        self.mean_dev = bt.indicators.MeanDev(self.typical_price, period=self.params.period)
        
        # Calculate CCI
        self.lines.cci = (
            (self.typical_price - self.sma_tp) / 
            (0.015 * self.mean_dev)
        )


class AverageDirectionalIndex(bt.Indicator):
    """Average Directional Index (ADX) indicator.
    
    Measures the strength of a trend regardless of direction.
    """
    
    lines = ('adx', 'plus_di', 'minus_di')
    params = (('period', 14),)
    
    def __init__(self):
        # Calculate True Range
        self.tr = bt.indicators.TrueRange(self.data)
        
        # Calculate Directional Movement
        self.plus_dm = bt.indicators.PlusDM(self.data)
        self.minus_dm = bt.indicators.MinusDM(self.data)
        
        # Calculate Directional Indicators
        self.lines.plus_di = 100 * (
            bt.indicators.SmoothedMovingAverage(self.plus_dm, period=self.params.period) /
            bt.indicators.SmoothedMovingAverage(self.tr, period=self.params.period)
        )
        
        self.lines.minus_di = 100 * (
            bt.indicators.SmoothedMovingAverage(self.minus_dm, period=self.params.period) /
            bt.indicators.SmoothedMovingAverage(self.tr, period=self.params.period)
        )
        
        # Calculate DX
        dx = 100 * (
            abs(self.lines.plus_di - self.lines.minus_di) /
            (self.lines.plus_di + self.lines.minus_di)
        )
        
        # Calculate ADX
        self.lines.adx = bt.indicators.SmoothedMovingAverage(dx, period=self.params.period)


class ParabolicSAR(bt.Indicator):
    """Parabolic SAR (Stop and Reverse) indicator.
    
    Trend-following indicator that provides entry and exit points.
    """
    
    lines = ('sar',)
    params = (
        ('af_start', 0.02),
        ('af_increment', 0.02),
        ('af_maximum', 0.20),
    )
    
    def __init__(self):
        self.addminperiod(2)
    
    def next(self):
        if len(self) == 1:
            # Initialize
            self.trend = 1  # 1 for uptrend, -1 for downtrend
            self.af = self.params.af_start
            self.ep = self.data.high[0]  # Extreme Point
            self.lines.sar[0] = self.data.low[0]
            return
        
        # Previous values
        prev_sar = self.lines.sar[-1]
        
        if self.trend == 1:  # Uptrend
            # Calculate SAR
            sar = prev_sar + self.af * (self.ep - prev_sar)
            
            # Check for trend reversal
            if self.data.low[0] <= sar:
                # Trend reversal to downtrend
                self.trend = -1
                self.lines.sar[0] = self.ep
                self.af = self.params.af_start
                self.ep = self.data.low[0]
            else:
                # Continue uptrend
                self.lines.sar[0] = min(sar, self.data.low[-1], self.data.low[0])
                
                # Update extreme point and acceleration factor
                if self.data.high[0] > self.ep:
                    self.ep = self.data.high[0]
                    self.af = min(self.af + self.params.af_increment, self.params.af_maximum)
        
        else:  # Downtrend
            # Calculate SAR
            sar = prev_sar + self.af * (self.ep - prev_sar)
            
            # Check for trend reversal
            if self.data.high[0] >= sar:
                # Trend reversal to uptrend
                self.trend = 1
                self.lines.sar[0] = self.ep
                self.af = self.params.af_start
                self.ep = self.data.high[0]
            else:
                # Continue downtrend
                self.lines.sar[0] = max(sar, self.data.high[-1], self.data.high[0])
                
                # Update extreme point and acceleration factor
                if self.data.low[0] < self.ep:
                    self.ep = self.data.low[0]
                    self.af = min(self.af + self.params.af_increment, self.params.af_maximum)


class IchimokuCloud(bt.Indicator):
    """Ichimoku Cloud indicator.
    
    Comprehensive indicator that defines support/resistance, trend direction, and momentum.
    """
    
    lines = ('tenkan_sen', 'kijun_sen', 'senkou_span_a', 'senkou_span_b', 'chikou_span')
    params = (
        ('tenkan_period', 9),
        ('kijun_period', 26),
        ('senkou_period', 52),
    )
    
    def __init__(self):
        # Tenkan-sen (Conversion Line)
        tenkan_high = bt.indicators.Highest(self.data.high, period=self.params.tenkan_period)
        tenkan_low = bt.indicators.Lowest(self.data.low, period=self.params.tenkan_period)
        self.lines.tenkan_sen = (tenkan_high + tenkan_low) / 2
        
        # Kijun-sen (Base Line)
        kijun_high = bt.indicators.Highest(self.data.high, period=self.params.kijun_period)
        kijun_low = bt.indicators.Lowest(self.data.low, period=self.params.kijun_period)
        self.lines.kijun_sen = (kijun_high + kijun_low) / 2
        
        # Senkou Span A (Leading Span A)
        self.lines.senkou_span_a = (self.lines.tenkan_sen + self.lines.kijun_sen) / 2
        
        # Senkou Span B (Leading Span B)
        senkou_high = bt.indicators.Highest(self.data.high, period=self.params.senkou_period)
        senkou_low = bt.indicators.Lowest(self.data.low, period=self.params.senkou_period)
        self.lines.senkou_span_b = (senkou_high + senkou_low) / 2
        
        # Chikou Span (Lagging Span)
        self.lines.chikou_span = self.data.close(-self.params.kijun_period)


class VolumeWeightedAveragePrice(bt.Indicator):
    """Volume Weighted Average Price (VWAP) indicator.
    
    Shows the average price weighted by volume.
    """
    
    lines = ('vwap',)
    params = (('period', 0),)  # 0 means cumulative from start
    
    def __init__(self):
        # Calculate typical price
        self.typical_price = (self.data.high + self.data.low + self.data.close) / 3
        
        # Calculate volume * typical price
        self.volume_price = self.typical_price * self.data.volume
        
        if self.params.period > 0:
            # Rolling VWAP
            self.sum_volume_price = bt.indicators.SumN(self.volume_price, period=self.params.period)
            self.sum_volume = bt.indicators.SumN(self.data.volume, period=self.params.period)
        else:
            # Cumulative VWAP
            self.sum_volume_price = bt.indicators.SumN(self.volume_price, period=len(self.data))
            self.sum_volume = bt.indicators.SumN(self.data.volume, period=len(self.data))
        
        self.lines.vwap = self.sum_volume_price / self.sum_volume


class MoneyFlowIndex(bt.Indicator):
    """Money Flow Index (MFI) indicator.
    
    Volume-weighted version of RSI.
    """
    
    lines = ('mfi',)
    params = (('period', 14),)
    
    def __init__(self):
        # Calculate typical price
        self.typical_price = (self.data.high + self.data.low + self.data.close) / 3
        
        # Calculate raw money flow
        self.money_flow = self.typical_price * self.data.volume
        
        # Calculate positive and negative money flow
        self.positive_mf = bt.If(
            self.typical_price > self.typical_price(-1),
            self.money_flow,
            0
        )
        
        self.negative_mf = bt.If(
            self.typical_price < self.typical_price(-1),
            self.money_flow,
            0
        )
        
        # Calculate money flow ratio
        self.positive_mf_sum = bt.indicators.SumN(self.positive_mf, period=self.params.period)
        self.negative_mf_sum = bt.indicators.SumN(self.negative_mf, period=self.params.period)
        
        self.money_ratio = self.positive_mf_sum / self.negative_mf_sum
        
        # Calculate MFI
        self.lines.mfi = 100 - (100 / (1 + self.money_ratio))


class ChaikinOscillator(bt.Indicator):
    """Chaikin Oscillator indicator.
    
    Measures momentum of Accumulation/Distribution line.
    """
    
    lines = ('chaikin_osc',)
    params = (
        ('fast_period', 3),
        ('slow_period', 10),
    )
    
    def __init__(self):
        # Calculate Money Flow Multiplier
        self.mfm = (
            (self.data.close - self.data.low) - (self.data.high - self.data.close)
        ) / (self.data.high - self.data.low)
        
        # Calculate Money Flow Volume
        self.mfv = self.mfm * self.data.volume
        
        # Calculate Accumulation/Distribution Line
        self.ad_line = bt.indicators.SumN(self.mfv, period=len(self.data))
        
        # Calculate Chaikin Oscillator
        self.fast_ema = bt.indicators.EMA(self.ad_line, period=self.params.fast_period)
        self.slow_ema = bt.indicators.EMA(self.ad_line, period=self.params.slow_period)
        
        self.lines.chaikin_osc = self.fast_ema - self.slow_ema
