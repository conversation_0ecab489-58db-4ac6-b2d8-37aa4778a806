"""
Configuration management for the trading system.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime


@dataclass
class DataConfig:
    """Data configuration settings."""
    source: str = "yahoo"
    symbols: list = field(default_factory=lambda: ["AAPL", "MSFT", "GOOGL"])
    start_date: str = "2020-01-01"
    end_date: str = "2023-12-31"
    timeframe: str = "1d"
    csv: Dict[str, Any] = field(default_factory=dict)
    yahoo: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BrokerConfig:
    """Broker configuration settings."""
    cash: float = 100000.0
    commission: float = 0.001
    commission_type: str = "percent"
    margin: Optional[float] = None
    mult: float = 1.0
    slip_perc: float = 0.0
    slip_fixed: float = 0.0
    slip_open: bool = False
    slip_match: bool = True


@dataclass
class StrategyConfig:
    """Strategy configuration settings."""
    name: str = "SMAStrategy"
    parameters: Dict[str, Any] = field(default_factory=dict)


@dataclass
class OptimizationConfig:
    """Optimization configuration settings."""
    optimize_params: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    objective: str = "sharpe_ratio"
    max_cpus: int = 4


@dataclass
class PlottingConfig:
    """Plotting configuration settings."""
    enabled: bool = True
    style: str = "candlestick"
    figsize: list = field(default_factory=lambda: [15, 10])
    volume: bool = True
    plot_indicators: bool = True


@dataclass
class LoggingConfig:
    """Logging configuration settings."""
    level: str = "INFO"
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}"
    file: str = "logs/trading_system.log"
    console: bool = True


@dataclass
class RiskManagementConfig:
    """Risk management configuration settings."""
    max_drawdown: float = 0.15
    max_position_size: float = 0.2
    max_positions: int = 5
    use_stop_loss: bool = True
    stop_loss_percent: float = 0.02
    use_take_profit: bool = True
    take_profit_percent: float = 0.06


@dataclass
class AnalysisConfig:
    """Analysis configuration settings."""
    analyzers: list = field(default_factory=lambda: ["SharpeRatio", "DrawDown", "TradeAnalyzer"])
    benchmark: str = "SPY"
    risk_free_rate: float = 0.02


@dataclass
class LiveTradingConfig:
    """Live trading configuration settings."""
    enabled: bool = False
    broker: str = "ib"
    paper_trading: bool = True
    ib: Dict[str, Any] = field(default_factory=dict)
    oanda: Dict[str, Any] = field(default_factory=dict)


class Config:
    """Main configuration class."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize configuration.
        
        Args:
            config_path: Path to configuration file. If None, uses default config.yaml
        """
        self.config_path = config_path or "config.yaml"
        self._config_data = self._load_config()
        
        # Initialize configuration sections
        self.data = DataConfig(**self._config_data.get("data", {}))
        self.broker = BrokerConfig(**self._config_data.get("broker", {}))
        self.strategy = StrategyConfig(**self._config_data.get("strategy", {}))
        self.optimization = OptimizationConfig(**self._config_data.get("optimization", {}))
        self.plotting = PlottingConfig(**self._config_data.get("plotting", {}))
        self.logging = LoggingConfig(**self._config_data.get("logging", {}))
        self.risk_management = RiskManagementConfig(**self._config_data.get("risk_management", {}))
        self.analysis = AnalysisConfig(**self._config_data.get("analysis", {}))
        self.live_trading = LiveTradingConfig(**self._config_data.get("live_trading", {}))
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file) or {}
        except FileNotFoundError:
            print(f"Warning: Configuration file {self.config_path} not found. Using defaults.")
            return {}
        except yaml.YAMLError as e:
            print(f"Error parsing configuration file: {e}")
            return {}
    
    def save_config(self, path: Optional[str] = None) -> None:
        """Save current configuration to file.
        
        Args:
            path: Path to save configuration. If None, uses original path.
        """
        save_path = path or self.config_path
        
        config_dict = {
            "data": self.data.__dict__,
            "broker": self.broker.__dict__,
            "strategy": self.strategy.__dict__,
            "optimization": self.optimization.__dict__,
            "plotting": self.plotting.__dict__,
            "logging": self.logging.__dict__,
            "risk_management": self.risk_management.__dict__,
            "analysis": self.analysis.__dict__,
            "live_trading": self.live_trading.__dict__,
        }
        
        # Ensure directory exists
        Path(save_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(save_path, 'w', encoding='utf-8') as file:
            yaml.dump(config_dict, file, default_flow_style=False, indent=2)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key.
        
        Args:
            key: Configuration key (supports dot notation, e.g., 'data.symbols')
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        keys = key.split('.')
        value = self._config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def update(self, key: str, value: Any) -> None:
        """Update configuration value.
        
        Args:
            key: Configuration key (supports dot notation)
            value: New value
        """
        keys = key.split('.')
        config = self._config_data
        
        # Navigate to the parent of the target key
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set the value
        config[keys[-1]] = value
        
        # Reload configuration objects
        self.__init__(self.config_path)


# Global configuration instance
config = Config()
