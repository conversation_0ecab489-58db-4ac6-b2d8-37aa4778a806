#!/usr/bin/env python3
"""
Main execution script for the Backtrader Trading System.
"""

import argparse
import sys
from pathlib import Path

import backtrader as bt

from .config import config
from .utils.logger import setup_logger
from .utils.data_utils import DataUtils
from .utils.performance import PerformanceAnalyzer
from .strategies import get_strategy


def setup_cerebro():
    """Setup and configure Cerebro engine."""
    cerebro = bt.Cerebro()
    
    # Set broker configuration
    cerebro.broker.setcash(config.broker.cash)
    cerebro.broker.setcommission(commission=config.broker.commission)
    
    # Add sizer
    cerebro.addsizer(bt.sizers.PercentSizer, percents=config.strategy.parameters.get('position_size', 10) * 100)
    
    return cerebro


def add_analyzers(cerebro):
    """Add analyzers to Cerebro."""
    analyzers = config.analysis.analyzers
    
    if "SharpeRatio" in analyzers:
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    
    if "DrawDown" in analyzers:
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    
    if "TradeAnalyzer" in analyzers:
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    
    if "Returns" in analyzers:
        cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
    
    if "TimeReturn" in analyzers:
        cerebro.addanalyzer(bt.analyzers.TimeReturn, _name='timereturn')


def run_backtest(
    strategy_name=None,
    symbols=None,
    start_date=None,
    end_date=None,
    plot=None,
    **strategy_params
):
    """Run backtest with specified parameters.
    
    Args:
        strategy_name: Name of strategy to use
        symbols: List of symbols to trade
        start_date: Start date for backtest
        end_date: End date for backtest
        plot: Whether to plot results
        **strategy_params: Additional strategy parameters
    """
    # Setup logger
    logger = setup_logger(
        level=config.logging.level,
        log_file=config.logging.file,
        console=config.logging.console
    )
    
    logger.info("Starting backtest...")
    
    # Use config defaults if not specified
    strategy_name = strategy_name or config.strategy.name
    symbols = symbols or config.data.symbols
    start_date = start_date or config.data.start_date
    end_date = end_date or config.data.end_date
    plot = plot if plot is not None else config.plotting.enabled
    
    # Setup Cerebro
    cerebro = setup_cerebro()
    
    # Get strategy class
    try:
        strategy_class = get_strategy(strategy_name)
    except ValueError as e:
        logger.error(f"Strategy error: {e}")
        return None
    
    # Merge strategy parameters
    merged_params = {**config.strategy.parameters, **strategy_params}
    
    # Add strategy
    cerebro.addstrategy(strategy_class, **merged_params)
    
    # Add analyzers
    add_analyzers(cerebro)
    
    # Get data feeds
    data_utils = DataUtils()
    data_feeds = data_utils.get_data_feeds(symbols=symbols)
    
    if not data_feeds:
        logger.error("No data feeds available")
        return None
    
    # Add data feeds to Cerebro
    for feed in data_feeds:
        cerebro.adddata(feed)
    
    # Log starting conditions
    starting_value = cerebro.broker.getvalue()
    logger.info(f"Starting Portfolio Value: {starting_value:.2f}")
    logger.info(f"Strategy: {strategy_name}")
    logger.info(f"Symbols: {symbols}")
    logger.info(f"Period: {start_date} to {end_date}")
    
    # Run backtest
    results = cerebro.run()
    
    # Log final results
    final_value = cerebro.broker.getvalue()
    total_return = (final_value - starting_value) / starting_value * 100
    
    logger.info(f"Final Portfolio Value: {final_value:.2f}")
    logger.info(f"Total Return: {total_return:.2f}%")
    
    # Print analyzer results
    if results:
        strategy_result = results[0]
        print_analyzer_results(strategy_result)
    
    # Plot results if requested
    if plot:
        try:
            cerebro.plot(style='candlestick', volume=config.plotting.volume)
        except Exception as e:
            logger.warning(f"Plotting failed: {e}")
    
    logger.info("Backtest completed")
    return results


def print_analyzer_results(strategy):
    """Print analyzer results."""
    print("\n" + "="*60)
    print("ANALYZER RESULTS")
    print("="*60)
    
    # Sharpe Ratio
    if hasattr(strategy.analyzers, 'sharpe'):
        sharpe = strategy.analyzers.sharpe.get_analysis()
        print(f"Sharpe Ratio: {sharpe.get('sharperatio', 'N/A')}")
    
    # Drawdown
    if hasattr(strategy.analyzers, 'drawdown'):
        drawdown = strategy.analyzers.drawdown.get_analysis()
        print(f"Max Drawdown: {drawdown.get('max', {}).get('drawdown', 'N/A'):.2%}")
        print(f"Max Drawdown Duration: {drawdown.get('max', {}).get('len', 'N/A')} days")
    
    # Trade Analysis
    if hasattr(strategy.analyzers, 'trades'):
        trades = strategy.analyzers.trades.get_analysis()
        total_trades = trades.get('total', {}).get('total', 0)
        won_trades = trades.get('won', {}).get('total', 0)
        lost_trades = trades.get('lost', {}).get('total', 0)
        
        print(f"Total Trades: {total_trades}")
        print(f"Won Trades: {won_trades}")
        print(f"Lost Trades: {lost_trades}")
        
        if total_trades > 0:
            win_rate = won_trades / total_trades * 100
            print(f"Win Rate: {win_rate:.1f}%")
        
        if 'pnl' in trades:
            avg_win = trades['won'].get('pnl', {}).get('average', 0)
            avg_loss = trades['lost'].get('pnl', {}).get('average', 0)
            print(f"Average Win: {avg_win:.2f}")
            print(f"Average Loss: {avg_loss:.2f}")
            
            if avg_loss != 0:
                profit_factor = abs(avg_win / avg_loss)
                print(f"Profit Factor: {profit_factor:.2f}")
    
    print("="*60)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Backtrader Trading System")
    
    parser.add_argument(
        '--strategy', 
        type=str, 
        help='Strategy name to use'
    )
    
    parser.add_argument(
        '--symbols', 
        nargs='+', 
        help='Symbols to trade'
    )
    
    parser.add_argument(
        '--start-date', 
        type=str, 
        help='Start date (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--end-date', 
        type=str, 
        help='End date (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--plot', 
        action='store_true', 
        help='Plot results'
    )
    
    parser.add_argument(
        '--no-plot', 
        action='store_true', 
        help='Disable plotting'
    )
    
    parser.add_argument(
        '--config', 
        type=str, 
        help='Path to configuration file'
    )
    
    # Strategy parameters
    parser.add_argument(
        '--fast-period', 
        type=int, 
        help='Fast period for moving averages'
    )
    
    parser.add_argument(
        '--slow-period', 
        type=int, 
        help='Slow period for moving averages'
    )
    
    parser.add_argument(
        '--rsi-period', 
        type=int, 
        help='RSI period'
    )
    
    parser.add_argument(
        '--stop-loss', 
        type=float, 
        help='Stop loss percentage'
    )
    
    parser.add_argument(
        '--take-profit', 
        type=float, 
        help='Take profit percentage'
    )
    
    args = parser.parse_args()
    
    # Load custom config if specified
    if args.config:
        from .config import Config
        global config
        config = Config(args.config)
    
    # Prepare strategy parameters
    strategy_params = {}
    if args.fast_period is not None:
        strategy_params['fast_period'] = args.fast_period
    if args.slow_period is not None:
        strategy_params['slow_period'] = args.slow_period
    if args.rsi_period is not None:
        strategy_params['rsi_period'] = args.rsi_period
    if args.stop_loss is not None:
        strategy_params['stop_loss'] = args.stop_loss
    if args.take_profit is not None:
        strategy_params['take_profit'] = args.take_profit
    
    # Determine plot setting
    plot = None
    if args.plot:
        plot = True
    elif args.no_plot:
        plot = False
    
    # Run backtest
    try:
        results = run_backtest(
            strategy_name=args.strategy,
            symbols=args.symbols,
            start_date=args.start_date,
            end_date=args.end_date,
            plot=plot,
            **strategy_params
        )
        
        if results is None:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\nBacktest interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Error running backtest: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
