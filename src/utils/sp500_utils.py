"""
Utilities for managing S&P 500 ticker symbols and data.
"""

import pandas as pd
import requests
from typing import List, Optional, Dict
import yfinance as yf
from datetime import datetime, timedelta
import time
from ..utils.logger import get_logger

logger = get_logger(__name__)


class SP500Utils:
    """Utility class for S&P 500 ticker management and data operations."""
    
    def __init__(self):
        self.tickers_cache = None
        self.cache_timestamp = None
        self.cache_duration = timedelta(hours=24)  # Cache for 24 hours
    
    def get_sp500_tickers(self, use_cache: bool = True) -> List[str]:
        """Get current S&P 500 ticker symbols from Wikipedia.
        
        Args:
            use_cache: Whether to use cached tickers if available
            
        Returns:
            List of S&P 500 ticker symbols
        """
        # Check cache first
        if (use_cache and self.tickers_cache and self.cache_timestamp and 
            datetime.now() - self.cache_timestamp < self.cache_duration):
            logger.info(f"Using cached S&P 500 tickers ({len(self.tickers_cache)} symbols)")
            return self.tickers_cache
        
        try:
            logger.info("Fetching S&P 500 tickers from Wikipedia...")
            
            # Scrape S&P 500 list from Wikipedia
            url = "https://en.wikipedia.org/wiki/List_of_S%26P_500_companies"
            tables = pd.read_html(url)
            
            # The first table contains the current S&P 500 companies
            sp500_table = tables[0]
            
            # Extract ticker symbols (usually in 'Symbol' column)
            if 'Symbol' in sp500_table.columns:
                tickers = sp500_table['Symbol'].tolist()
            elif 'Ticker' in sp500_table.columns:
                tickers = sp500_table['Ticker'].tolist()
            else:
                # Fallback: assume first column contains tickers
                tickers = sp500_table.iloc[:, 0].tolist()
            
            # Clean tickers (remove any special characters, ensure uppercase)
            tickers = [ticker.strip().upper().replace('.', '-') for ticker in tickers if ticker]
            
            # Remove any invalid tickers
            tickers = [ticker for ticker in tickers if ticker and len(ticker) <= 5]
            
            # Cache the results
            self.tickers_cache = tickers
            self.cache_timestamp = datetime.now()
            
            logger.info(f"Successfully fetched {len(tickers)} S&P 500 tickers")
            return tickers
            
        except Exception as e:
            logger.error(f"Failed to fetch S&P 500 tickers from Wikipedia: {e}")
            
            # Fallback to a static list of major S&P 500 companies
            fallback_tickers = self._get_fallback_tickers()
            logger.warning(f"Using fallback ticker list with {len(fallback_tickers)} symbols")
            return fallback_tickers
    
    def _get_fallback_tickers(self) -> List[str]:
        """Get a fallback list of major S&P 500 tickers."""
        return [
            'AAPL', 'MSFT', 'AMZN', 'NVDA', 'GOOGL', 'GOOG', 'META', 'TSLA', 'BRK-B', 'UNH',
            'JNJ', 'XOM', 'JPM', 'V', 'PG', 'MA', 'HD', 'CVX', 'ABBV', 'PFE',
            'BAC', 'KO', 'AVGO', 'PEP', 'TMO', 'COST', 'WMT', 'DIS', 'ABT', 'MRK',
            'ACN', 'VZ', 'ADBE', 'DHR', 'NEE', 'BMY', 'TXN', 'LIN', 'PM', 'RTX',
            'NFLX', 'CRM', 'ORCL', 'WFC', 'AMD', 'T', 'QCOM', 'UPS', 'AMGN', 'HON',
            'SBUX', 'LOW', 'IBM', 'ELV', 'CAT', 'GS', 'AXP', 'BLK', 'DE', 'BKNG',
            'GILD', 'ADP', 'TJX', 'SYK', 'MDLZ', 'ADI', 'CVS', 'CI', 'TMUS', 'AMT',
            'ISRG', 'MMC', 'LRCX', 'C', 'SO', 'ZTS', 'PLD', 'CB', 'DUK', 'SCHW',
            'FI', 'BSX', 'MU', 'ETN', 'AON', 'CSX', 'ITW', 'CL', 'CME', 'EOG',
            'ICE', 'PNC', 'FCX', 'USB', 'APD', 'WM', 'GE', 'SHW', 'MCO', 'COP'
        ]
    
    def validate_tickers(self, tickers: List[str], max_workers: int = 10) -> List[str]:
        """Validate that tickers have available data on Yahoo Finance.
        
        Args:
            tickers: List of ticker symbols to validate
            max_workers: Maximum number of concurrent requests
            
        Returns:
            List of valid ticker symbols
        """
        valid_tickers = []
        batch_size = 50  # Process in batches to avoid overwhelming the API
        
        logger.info(f"Validating {len(tickers)} tickers...")
        
        for i in range(0, len(tickers), batch_size):
            batch = tickers[i:i + batch_size]
            
            try:
                # Try to download a small amount of data for validation
                data = yf.download(
                    batch, 
                    period="5d", 
                    interval="1d",
                    group_by='ticker',
                    auto_adjust=True,
                    prepost=True,
                    threads=True,
                    progress=False
                )
                
                if len(batch) == 1:
                    # Single ticker case
                    if not data.empty and len(data) > 0:
                        valid_tickers.append(batch[0])
                else:
                    # Multiple tickers case
                    for ticker in batch:
                        try:
                            ticker_data = data[ticker] if ticker in data.columns.levels[0] else None
                            if ticker_data is not None and not ticker_data.empty and len(ticker_data) > 0:
                                valid_tickers.append(ticker)
                        except (KeyError, AttributeError):
                            continue
                
                # Rate limiting
                time.sleep(0.1)
                
            except Exception as e:
                logger.warning(f"Error validating batch {i//batch_size + 1}: {e}")
                continue
        
        logger.info(f"Validated {len(valid_tickers)} out of {len(tickers)} tickers")
        return valid_tickers
    
    def get_sector_info(self, tickers: List[str]) -> Dict[str, str]:
        """Get sector information for S&P 500 tickers.
        
        Args:
            tickers: List of ticker symbols
            
        Returns:
            Dictionary mapping ticker to sector
        """
        sector_map = {}
        
        try:
            # Get sector info from Wikipedia table
            url = "https://en.wikipedia.org/wiki/List_of_S%26P_500_companies"
            tables = pd.read_html(url)
            sp500_table = tables[0]
            
            # Create mapping from symbol to sector
            if 'Symbol' in sp500_table.columns and 'GICS Sector' in sp500_table.columns:
                for _, row in sp500_table.iterrows():
                    symbol = row['Symbol'].strip().upper().replace('.', '-')
                    sector = row['GICS Sector']
                    if symbol in tickers:
                        sector_map[symbol] = sector
            
        except Exception as e:
            logger.warning(f"Could not fetch sector information: {e}")
        
        return sector_map
    
    def filter_by_liquidity(self, tickers: List[str], min_volume: int = 1000000) -> List[str]:
        """Filter tickers by minimum average daily volume.
        
        Args:
            tickers: List of ticker symbols
            min_volume: Minimum average daily volume
            
        Returns:
            List of liquid ticker symbols
        """
        liquid_tickers = []
        
        logger.info(f"Filtering {len(tickers)} tickers by liquidity (min volume: {min_volume:,})")
        
        batch_size = 20
        for i in range(0, len(tickers), batch_size):
            batch = tickers[i:i + batch_size]
            
            try:
                # Download recent volume data
                data = yf.download(
                    batch,
                    period="1mo",
                    interval="1d",
                    group_by='ticker',
                    auto_adjust=True,
                    progress=False
                )
                
                for ticker in batch:
                    try:
                        if len(batch) == 1:
                            volume_data = data['Volume'] if 'Volume' in data.columns else None
                        else:
                            volume_data = data[ticker]['Volume'] if ticker in data.columns.levels[0] else None
                        
                        if volume_data is not None and not volume_data.empty:
                            avg_volume = volume_data.mean()
                            if avg_volume >= min_volume:
                                liquid_tickers.append(ticker)
                    
                    except (KeyError, AttributeError):
                        continue
                
                time.sleep(0.1)  # Rate limiting
                
            except Exception as e:
                logger.warning(f"Error checking liquidity for batch: {e}")
                continue
        
        logger.info(f"Found {len(liquid_tickers)} liquid tickers")
        return liquid_tickers
    
    def get_market_cap_tiers(self, tickers: List[str]) -> Dict[str, List[str]]:
        """Categorize tickers by market cap tiers.
        
        Args:
            tickers: List of ticker symbols
            
        Returns:
            Dictionary with 'large', 'mid', 'small' cap lists
        """
        # This is a simplified categorization
        # In practice, you'd want to fetch actual market cap data
        
        large_cap = []
        mid_cap = []
        small_cap = []
        
        # Top 100 by typical market cap (simplified)
        mega_caps = [
            'AAPL', 'MSFT', 'AMZN', 'NVDA', 'GOOGL', 'GOOG', 'META', 'TSLA', 'BRK-B', 'UNH',
            'JNJ', 'XOM', 'JPM', 'V', 'PG', 'MA', 'HD', 'CVX', 'ABBV', 'PFE',
            'BAC', 'KO', 'AVGO', 'PEP', 'TMO', 'COST', 'WMT', 'DIS', 'ABT', 'MRK',
            'ACN', 'VZ', 'ADBE', 'DHR', 'NEE', 'BMY', 'TXN', 'LIN', 'PM', 'RTX',
            'NFLX', 'CRM', 'ORCL', 'WFC', 'AMD', 'T', 'QCOM', 'UPS', 'AMGN', 'HON'
        ]
        
        for ticker in tickers:
            if ticker in mega_caps[:50]:
                large_cap.append(ticker)
            elif ticker in mega_caps:
                mid_cap.append(ticker)
            else:
                small_cap.append(ticker)
        
        return {
            'large': large_cap,
            'mid': mid_cap,
            'small': small_cap
        }
