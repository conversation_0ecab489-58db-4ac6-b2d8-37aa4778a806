"""
Logging utilities for the trading system.
"""

import sys
from pathlib import Path
from loguru import logger
from typing import Optional


def setup_logger(
    level: str = "INFO",
    log_file: Optional[str] = None,
    format_string: Optional[str] = None,
    console: bool = True
) -> logger:
    """Setup logger with specified configuration.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file. If None, no file logging.
        format_string: Custom format string for logs
        console: Whether to log to console
        
    Returns:
        Configured logger instance
    """
    # Remove default handler
    logger.remove()
    
    # Default format
    if format_string is None:
        format_string = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
    
    # Add console handler if requested
    if console:
        logger.add(
            sys.stdout,
            format=format_string,
            level=level,
            colorize=True,
            backtrace=True,
            diagnose=True
        )
    
    # Add file handler if log_file is specified
    if log_file:
        # Ensure log directory exists
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_file,
            format=format_string,
            level=level,
            rotation="10 MB",
            retention="30 days",
            compression="zip",
            backtrace=True,
            diagnose=True
        )
    
    return logger


def get_logger(name: str) -> logger:
    """Get a logger instance with the specified name.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logger.bind(name=name)


# Trading-specific loggers
def get_strategy_logger(strategy_name: str) -> logger:
    """Get a logger for strategy-specific logging.
    
    Args:
        strategy_name: Name of the strategy
        
    Returns:
        Strategy logger instance
    """
    return logger.bind(name=f"strategy.{strategy_name}")


def get_data_logger() -> logger:
    """Get a logger for data-related operations.
    
    Returns:
        Data logger instance
    """
    return logger.bind(name="data")


def get_broker_logger() -> logger:
    """Get a logger for broker-related operations.
    
    Returns:
        Broker logger instance
    """
    return logger.bind(name="broker")


def get_analysis_logger() -> logger:
    """Get a logger for analysis operations.
    
    Returns:
        Analysis logger instance
    """
    return logger.bind(name="analysis")


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.logger = get_logger(self.__class__.__name__)
    
    def log_info(self, message: str) -> None:
        """Log info message."""
        self.logger.info(message)
    
    def log_warning(self, message: str) -> None:
        """Log warning message."""
        self.logger.warning(message)
    
    def log_error(self, message: str) -> None:
        """Log error message."""
        self.logger.error(message)
    
    def log_debug(self, message: str) -> None:
        """Log debug message."""
        self.logger.debug(message)
    
    def log_critical(self, message: str) -> None:
        """Log critical message."""
        self.logger.critical(message)
