"""
Data utilities for the trading system.
"""

import pandas as pd
import yfinance as yf
import backtrader as bt
from pathlib import Path
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timedelta

from ..config import config
from .logger import get_data_logger


class DataUtils:
    """Utility class for data operations."""
    
    def __init__(self):
        self.logger = get_data_logger()
    
    def download_yahoo_data(
        self,
        symbols: Union[str, List[str]],
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        period: str = "1y",
        interval: str = "1d",
        save_to_csv: bool = False,
        csv_dir: str = "data/csv"
    ) -> Dict[str, pd.DataFrame]:
        """Download data from Yahoo Finance.
        
        Args:
            symbols: Symbol or list of symbols to download
            start_date: Start date (YYYY-MM-DD format)
            end_date: End date (YYYY-MM-DD format)
            period: Period to download (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
            interval: Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)
            save_to_csv: Whether to save data to CSV files
            csv_dir: Directory to save CSV files
            
        Returns:
            Dictionary with symbol as key and DataFrame as value
        """
        if isinstance(symbols, str):
            symbols = [symbols]
        
        self.logger.info(f"Downloading data for symbols: {symbols}")
        
        data_dict = {}
        
        for symbol in symbols:
            try:
                ticker = yf.Ticker(symbol)
                
                if start_date and end_date:
                    data = ticker.history(start=start_date, end=end_date, interval=interval)
                else:
                    data = ticker.history(period=period, interval=interval)
                
                if data.empty:
                    self.logger.warning(f"No data found for symbol: {symbol}")
                    continue
                
                # Clean column names
                data.columns = [col.replace(' ', '_').lower() for col in data.columns]
                
                # Ensure we have the required columns
                required_cols = ['open', 'high', 'low', 'close', 'volume']
                if not all(col in data.columns for col in required_cols):
                    self.logger.warning(f"Missing required columns for {symbol}: {data.columns}")
                    continue
                
                data_dict[symbol] = data
                self.logger.info(f"Downloaded {len(data)} rows for {symbol}")
                
                # Save to CSV if requested
                if save_to_csv:
                    csv_path = Path(csv_dir)
                    csv_path.mkdir(parents=True, exist_ok=True)
                    file_path = csv_path / f"{symbol}.csv"
                    data.to_csv(file_path)
                    self.logger.info(f"Saved {symbol} data to {file_path}")
                    
            except Exception as e:
                self.logger.error(f"Error downloading data for {symbol}: {e}")
        
        return data_dict
    
    def load_csv_data(
        self,
        file_path: str,
        symbol: Optional[str] = None,
        date_column: str = "Date",
        parse_dates: bool = True
    ) -> pd.DataFrame:
        """Load data from CSV file.
        
        Args:
            file_path: Path to CSV file
            symbol: Symbol name (for logging)
            date_column: Name of the date column
            parse_dates: Whether to parse dates
            
        Returns:
            DataFrame with loaded data
        """
        try:
            if parse_dates:
                data = pd.read_csv(file_path, index_col=date_column, parse_dates=True)
            else:
                data = pd.read_csv(file_path)
            
            # Clean column names
            data.columns = [col.replace(' ', '_').lower() for col in data.columns]
            
            symbol_name = symbol or Path(file_path).stem
            self.logger.info(f"Loaded {len(data)} rows for {symbol_name} from {file_path}")
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error loading CSV data from {file_path}: {e}")
            return pd.DataFrame()
    
    def create_backtrader_feed(
        self,
        data: pd.DataFrame,
        name: str = "data",
        timeframe: str = "1d"
    ) -> bt.feeds.PandasData:
        """Create Backtrader data feed from pandas DataFrame.
        
        Args:
            data: DataFrame with OHLCV data
            name: Name for the data feed
            timeframe: Timeframe for the data
            
        Returns:
            Backtrader data feed
        """
        try:
            # Ensure data is sorted by date
            data = data.sort_index()
            
            # Map timeframe to Backtrader constants
            timeframe_map = {
                "1m": bt.TimeFrame.Minutes,
                "5m": bt.TimeFrame.Minutes,
                "15m": bt.TimeFrame.Minutes,
                "30m": bt.TimeFrame.Minutes,
                "1h": bt.TimeFrame.Minutes,
                "1d": bt.TimeFrame.Days,
                "1w": bt.TimeFrame.Weeks,
                "1M": bt.TimeFrame.Months,
            }
            
            compression_map = {
                "1m": 1,
                "5m": 5,
                "15m": 15,
                "30m": 30,
                "1h": 60,
                "1d": 1,
                "1w": 1,
                "1M": 1,
            }
            
            bt_timeframe = timeframe_map.get(timeframe, bt.TimeFrame.Days)
            compression = compression_map.get(timeframe, 1)
            
            # Create data feed
            data_feed = bt.feeds.PandasData(
                dataname=data,
                name=name,
                timeframe=bt_timeframe,
                compression=compression,
                datetime=None,  # Use index as datetime
                open='open',
                high='high',
                low='low',
                close='close',
                volume='volume',
                openinterest=-1  # No open interest data
            )
            
            self.logger.info(f"Created Backtrader feed for {name} with {len(data)} bars")
            return data_feed
            
        except Exception as e:
            self.logger.error(f"Error creating Backtrader feed: {e}")
            raise
    
    def get_data_feeds(
        self,
        symbols: Optional[List[str]] = None,
        source: Optional[str] = None,
        **kwargs
    ) -> List[bt.feeds.PandasData]:
        """Get data feeds for backtesting.
        
        Args:
            symbols: List of symbols to get data for
            source: Data source ('yahoo', 'csv', etc.)
            **kwargs: Additional arguments for data loading
            
        Returns:
            List of Backtrader data feeds
        """
        symbols = symbols or config.data.symbols
        source = source or config.data.source
        
        data_feeds = []
        
        if source == "yahoo":
            data_dict = self.download_yahoo_data(
                symbols=symbols,
                start_date=config.data.start_date,
                end_date=config.data.end_date,
                **kwargs
            )
            
            for symbol, data in data_dict.items():
                feed = self.create_backtrader_feed(data, name=symbol)
                data_feeds.append(feed)
                
        elif source == "csv":
            csv_dir = config.data.csv.get("directory", "data/csv")
            
            for symbol in symbols:
                file_path = Path(csv_dir) / f"{symbol}.csv"
                if file_path.exists():
                    data = self.load_csv_data(str(file_path), symbol)
                    if not data.empty:
                        feed = self.create_backtrader_feed(data, name=symbol)
                        data_feeds.append(feed)
                else:
                    self.logger.warning(f"CSV file not found for {symbol}: {file_path}")
        
        self.logger.info(f"Created {len(data_feeds)} data feeds")
        return data_feeds
    
    def validate_data(self, data: pd.DataFrame, symbol: str = "Unknown") -> bool:
        """Validate data quality.
        
        Args:
            data: DataFrame to validate
            symbol: Symbol name for logging
            
        Returns:
            True if data is valid, False otherwise
        """
        if data.empty:
            self.logger.error(f"Data is empty for {symbol}")
            return False
        
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            self.logger.error(f"Missing columns for {symbol}: {missing_columns}")
            return False
        
        # Check for NaN values
        nan_counts = data[required_columns].isnull().sum()
        if nan_counts.any():
            self.logger.warning(f"NaN values found in {symbol}: {nan_counts.to_dict()}")
        
        # Check for negative values
        negative_counts = (data[required_columns] < 0).sum()
        if negative_counts.any():
            self.logger.warning(f"Negative values found in {symbol}: {negative_counts.to_dict()}")
        
        # Check for zero volume
        zero_volume = (data['volume'] == 0).sum()
        if zero_volume > 0:
            self.logger.warning(f"Zero volume bars found in {symbol}: {zero_volume}")
        
        self.logger.info(f"Data validation completed for {symbol}")
        return True
