"""
Performance analysis utilities for the trading system.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime

from .logger import get_analysis_logger


class PerformanceAnalyzer:
    """Utility class for performance analysis."""
    
    def __init__(self):
        self.logger = get_analysis_logger()
    
    def calculate_returns(self, prices: pd.Series) -> pd.Series:
        """Calculate returns from price series.
        
        Args:
            prices: Series of prices
            
        Returns:
            Series of returns
        """
        return prices.pct_change().dropna()
    
    def calculate_sharpe_ratio(
        self,
        returns: pd.Series,
        risk_free_rate: float = 0.02,
        periods_per_year: int = 252
    ) -> float:
        """Calculate Sharpe ratio.
        
        Args:
            returns: Series of returns
            risk_free_rate: Annual risk-free rate
            periods_per_year: Number of periods per year (252 for daily)
            
        Returns:
            Sharpe ratio
        """
        if len(returns) == 0 or returns.std() == 0:
            return 0.0
        
        excess_returns = returns - (risk_free_rate / periods_per_year)
        return np.sqrt(periods_per_year) * excess_returns.mean() / returns.std()
    
    def calculate_sortino_ratio(
        self,
        returns: pd.Series,
        risk_free_rate: float = 0.02,
        periods_per_year: int = 252
    ) -> float:
        """Calculate Sortino ratio.
        
        Args:
            returns: Series of returns
            risk_free_rate: Annual risk-free rate
            periods_per_year: Number of periods per year
            
        Returns:
            Sortino ratio
        """
        if len(returns) == 0:
            return 0.0
        
        excess_returns = returns - (risk_free_rate / periods_per_year)
        downside_returns = returns[returns < 0]
        
        if len(downside_returns) == 0 or downside_returns.std() == 0:
            return float('inf') if excess_returns.mean() > 0 else 0.0
        
        return np.sqrt(periods_per_year) * excess_returns.mean() / downside_returns.std()
    
    def calculate_max_drawdown(self, prices: pd.Series) -> Dict[str, Any]:
        """Calculate maximum drawdown.
        
        Args:
            prices: Series of prices or cumulative returns
            
        Returns:
            Dictionary with max drawdown info
        """
        if len(prices) == 0:
            return {"max_drawdown": 0.0, "max_drawdown_duration": 0}
        
        # Calculate running maximum
        running_max = prices.expanding().max()
        
        # Calculate drawdown
        drawdown = (prices - running_max) / running_max
        
        # Find maximum drawdown
        max_drawdown = drawdown.min()
        
        # Find maximum drawdown duration
        drawdown_start = None
        max_duration = 0
        current_duration = 0
        
        for i, dd in enumerate(drawdown):
            if dd < 0:
                if drawdown_start is None:
                    drawdown_start = i
                current_duration = i - drawdown_start + 1
            else:
                if current_duration > max_duration:
                    max_duration = current_duration
                drawdown_start = None
                current_duration = 0
        
        # Check if we ended in a drawdown
        if current_duration > max_duration:
            max_duration = current_duration
        
        return {
            "max_drawdown": abs(max_drawdown),
            "max_drawdown_duration": max_duration,
            "drawdown_series": drawdown
        }
    
    def calculate_calmar_ratio(
        self,
        returns: pd.Series,
        periods_per_year: int = 252
    ) -> float:
        """Calculate Calmar ratio (annual return / max drawdown).
        
        Args:
            returns: Series of returns
            periods_per_year: Number of periods per year
            
        Returns:
            Calmar ratio
        """
        if len(returns) == 0:
            return 0.0
        
        annual_return = (1 + returns).prod() ** (periods_per_year / len(returns)) - 1
        
        # Calculate cumulative returns for max drawdown
        cumulative_returns = (1 + returns).cumprod()
        max_dd_info = self.calculate_max_drawdown(cumulative_returns)
        max_drawdown = max_dd_info["max_drawdown"]
        
        if max_drawdown == 0:
            return float('inf') if annual_return > 0 else 0.0
        
        return annual_return / max_drawdown
    
    def calculate_var(
        self,
        returns: pd.Series,
        confidence_level: float = 0.05
    ) -> float:
        """Calculate Value at Risk (VaR).
        
        Args:
            returns: Series of returns
            confidence_level: Confidence level (e.g., 0.05 for 95% VaR)
            
        Returns:
            VaR value
        """
        if len(returns) == 0:
            return 0.0
        
        return np.percentile(returns, confidence_level * 100)
    
    def calculate_cvar(
        self,
        returns: pd.Series,
        confidence_level: float = 0.05
    ) -> float:
        """Calculate Conditional Value at Risk (CVaR).
        
        Args:
            returns: Series of returns
            confidence_level: Confidence level
            
        Returns:
            CVaR value
        """
        if len(returns) == 0:
            return 0.0
        
        var = self.calculate_var(returns, confidence_level)
        return returns[returns <= var].mean()
    
    def calculate_win_rate(self, returns: pd.Series) -> float:
        """Calculate win rate (percentage of positive returns).
        
        Args:
            returns: Series of returns
            
        Returns:
            Win rate as percentage
        """
        if len(returns) == 0:
            return 0.0
        
        return (returns > 0).mean() * 100
    
    def calculate_profit_factor(self, returns: pd.Series) -> float:
        """Calculate profit factor (gross profit / gross loss).
        
        Args:
            returns: Series of returns
            
        Returns:
            Profit factor
        """
        if len(returns) == 0:
            return 0.0
        
        gross_profit = returns[returns > 0].sum()
        gross_loss = abs(returns[returns < 0].sum())
        
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 0.0
        
        return gross_profit / gross_loss
    
    def generate_performance_report(
        self,
        returns: pd.Series,
        benchmark_returns: Optional[pd.Series] = None,
        risk_free_rate: float = 0.02,
        periods_per_year: int = 252
    ) -> Dict[str, Any]:
        """Generate comprehensive performance report.
        
        Args:
            returns: Series of strategy returns
            benchmark_returns: Series of benchmark returns (optional)
            risk_free_rate: Annual risk-free rate
            periods_per_year: Number of periods per year
            
        Returns:
            Dictionary with performance metrics
        """
        if len(returns) == 0:
            self.logger.warning("Empty returns series provided")
            return {}
        
        # Calculate cumulative returns
        cumulative_returns = (1 + returns).cumprod()
        
        # Basic metrics
        total_return = cumulative_returns.iloc[-1] - 1
        annual_return = (1 + total_return) ** (periods_per_year / len(returns)) - 1
        annual_volatility = returns.std() * np.sqrt(periods_per_year)
        
        # Risk metrics
        sharpe_ratio = self.calculate_sharpe_ratio(returns, risk_free_rate, periods_per_year)
        sortino_ratio = self.calculate_sortino_ratio(returns, risk_free_rate, periods_per_year)
        calmar_ratio = self.calculate_calmar_ratio(returns, periods_per_year)
        
        # Drawdown metrics
        max_dd_info = self.calculate_max_drawdown(cumulative_returns)
        
        # Risk metrics
        var_95 = self.calculate_var(returns, 0.05)
        cvar_95 = self.calculate_cvar(returns, 0.05)
        
        # Trading metrics
        win_rate = self.calculate_win_rate(returns)
        profit_factor = self.calculate_profit_factor(returns)
        
        report = {
            "total_return": total_return,
            "annual_return": annual_return,
            "annual_volatility": annual_volatility,
            "sharpe_ratio": sharpe_ratio,
            "sortino_ratio": sortino_ratio,
            "calmar_ratio": calmar_ratio,
            "max_drawdown": max_dd_info["max_drawdown"],
            "max_drawdown_duration": max_dd_info["max_drawdown_duration"],
            "var_95": var_95,
            "cvar_95": cvar_95,
            "win_rate": win_rate,
            "profit_factor": profit_factor,
            "total_trades": len(returns),
            "start_date": returns.index[0] if len(returns) > 0 else None,
            "end_date": returns.index[-1] if len(returns) > 0 else None,
        }
        
        # Benchmark comparison if provided
        if benchmark_returns is not None and len(benchmark_returns) > 0:
            # Align returns
            aligned_returns, aligned_benchmark = returns.align(benchmark_returns, join='inner')
            
            if len(aligned_returns) > 0:
                benchmark_total_return = (1 + aligned_benchmark).prod() - 1
                benchmark_annual_return = (1 + benchmark_total_return) ** (periods_per_year / len(aligned_benchmark)) - 1
                benchmark_volatility = aligned_benchmark.std() * np.sqrt(periods_per_year)
                
                # Calculate alpha and beta
                covariance = np.cov(aligned_returns, aligned_benchmark)[0, 1]
                benchmark_variance = aligned_benchmark.var()
                beta = covariance / benchmark_variance if benchmark_variance != 0 else 0
                alpha = annual_return - (risk_free_rate + beta * (benchmark_annual_return - risk_free_rate))
                
                # Information ratio
                excess_returns = aligned_returns - aligned_benchmark
                information_ratio = excess_returns.mean() / excess_returns.std() if excess_returns.std() != 0 else 0
                
                report.update({
                    "benchmark_total_return": benchmark_total_return,
                    "benchmark_annual_return": benchmark_annual_return,
                    "benchmark_volatility": benchmark_volatility,
                    "alpha": alpha,
                    "beta": beta,
                    "information_ratio": information_ratio,
                    "excess_return": total_return - benchmark_total_return,
                })
        
        self.logger.info("Performance report generated successfully")
        return report
    
    def print_performance_report(self, report: Dict[str, Any]) -> None:
        """Print formatted performance report.
        
        Args:
            report: Performance report dictionary
        """
        print("\n" + "="*60)
        print("PERFORMANCE REPORT")
        print("="*60)
        
        print(f"Period: {report.get('start_date', 'N/A')} to {report.get('end_date', 'N/A')}")
        print(f"Total Trades: {report.get('total_trades', 0)}")
        print()
        
        print("RETURNS:")
        print(f"  Total Return:        {report.get('total_return', 0):.2%}")
        print(f"  Annual Return:       {report.get('annual_return', 0):.2%}")
        print(f"  Annual Volatility:   {report.get('annual_volatility', 0):.2%}")
        print()
        
        print("RISK METRICS:")
        print(f"  Sharpe Ratio:        {report.get('sharpe_ratio', 0):.3f}")
        print(f"  Sortino Ratio:       {report.get('sortino_ratio', 0):.3f}")
        print(f"  Calmar Ratio:        {report.get('calmar_ratio', 0):.3f}")
        print(f"  Max Drawdown:        {report.get('max_drawdown', 0):.2%}")
        print(f"  VaR (95%):          {report.get('var_95', 0):.2%}")
        print(f"  CVaR (95%):         {report.get('cvar_95', 0):.2%}")
        print()
        
        print("TRADING METRICS:")
        print(f"  Win Rate:            {report.get('win_rate', 0):.1f}%")
        print(f"  Profit Factor:       {report.get('profit_factor', 0):.2f}")
        print()
        
        # Benchmark comparison if available
        if 'benchmark_annual_return' in report:
            print("BENCHMARK COMPARISON:")
            print(f"  Benchmark Return:    {report.get('benchmark_annual_return', 0):.2%}")
            print(f"  Excess Return:       {report.get('excess_return', 0):.2%}")
            print(f"  Alpha:               {report.get('alpha', 0):.2%}")
            print(f"  Beta:                {report.get('beta', 0):.3f}")
            print(f"  Information Ratio:   {report.get('information_ratio', 0):.3f}")
            print()
        
        print("="*60)
