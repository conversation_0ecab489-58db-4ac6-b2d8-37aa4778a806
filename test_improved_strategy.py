"""
Test the improved S&P 500 EMA strategy against the original.
"""

import backtrader as bt
import yfinance as yf
import pandas as pd
from datetime import datetime
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from src.strategies.sp500_ema_strategy import SP500EMAStrategy
from src.strategies.sp500_ema_strategy_improved import (
    SP500EMAStrategyImproved,
    SP500EMAStrategyImprovedConservative,
    SP500EMAStrategyImprovedAggressive
)
from src.utils.logger import get_logger

logger = get_logger(__name__)


def test_improved_vs_original():
    """Test improved strategy against original."""
    
    # Test with a focused set of tickers
    test_tickers = [
        'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'JPM', 'V', 'UNH',
        'JNJ', 'WMT', 'PG', 'HD', 'MA', 'DIS', 'ADBE', 'NFLX', 'CRM', 'PYPL'
    ]
    
    start_date = "2022-01-01"
    end_date = "2024-12-31"
    initial_cash = 100000.0
    
    logger.info("=" * 60)
    logger.info("IMPROVED vs ORIGINAL S&P 500 EMA Strategy Test")
    logger.info("=" * 60)
    
    # Download data
    logger.info(f"Downloading data for {len(test_tickers)} tickers...")
    try:
        data = yf.download(
            test_tickers,
            start=start_date,
            end=end_date,
            group_by='ticker',
            auto_adjust=True,
            progress=False
        )
        
        if data.empty:
            logger.error("No data downloaded")
            return
        
    except Exception as e:
        logger.error(f"Error downloading data: {e}")
        return
    
    # Create data feeds
    data_feeds = []
    successful_tickers = []
    
    for ticker in test_tickers:
        try:
            ticker_data = data[ticker].copy()
            
            if ticker_data.empty or len(ticker_data) < 100:
                logger.warning(f"Insufficient data for {ticker}")
                continue
            
            ticker_data = ticker_data.dropna()
            
            data_feed = bt.feeds.PandasData(
                dataname=ticker_data,
                name=ticker,
                fromdate=ticker_data.index[0],
                todate=ticker_data.index[-1],
                openinterest=None
            )
            data_feeds.append(data_feed)
            successful_tickers.append(ticker)
            
        except Exception as e:
            logger.warning(f"Error processing {ticker}: {e}")
            continue
    
    logger.info(f"Created {len(data_feeds)} data feeds for: {successful_tickers}")
    
    # Strategy configurations
    strategies = [
        ("Original", SP500EMAStrategy),
        ("Improved_Standard", SP500EMAStrategyImproved),
        ("Improved_Conservative", SP500EMAStrategyImprovedConservative),
        ("Improved_Aggressive", SP500EMAStrategyImprovedAggressive),
    ]
    
    results = {}
    
    for strategy_name, strategy_class in strategies:
        logger.info(f"\n{'='*50}")
        logger.info(f"Testing {strategy_name}")
        logger.info(f"{'='*50}")
        
        # Create Cerebro instance
        cerebro = bt.Cerebro()
        
        # Set broker parameters
        cerebro.broker.setcash(initial_cash)
        cerebro.broker.setcommission(commission=0.001)  # 0.1% commission
        
        # Add strategy
        cerebro.addstrategy(strategy_class, printlog=False)  # Reduce logging
        
        # Add data feeds
        for feed in data_feeds:
            cerebro.adddata(feed)
        
        # Add analyzers
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
        cerebro.addanalyzer(bt.analyzers.SQN, _name='sqn')
        
        # Run backtest
        try:
            results_list = cerebro.run()
            result = results_list[0]
            
            # Get final portfolio value
            final_value = cerebro.broker.getvalue()
            total_return = (final_value - initial_cash) / initial_cash * 100
            
            # Extract analyzer results
            sharpe_ratio = result.analyzers.sharpe.get_analysis().get('sharperatio', 0)
            drawdown = result.analyzers.drawdown.get_analysis()
            trades = result.analyzers.trades.get_analysis()
            sqn = result.analyzers.sqn.get_analysis().get('sqn', 0)
            
            # Calculate metrics
            total_trades = trades.get('total', {}).get('total', 0)
            winning_trades = trades.get('won', {}).get('total', 0)
            win_rate = (winning_trades / max(total_trades, 1)) * 100
            
            avg_win = trades.get('won', {}).get('pnl', {}).get('average', 0)
            avg_loss = trades.get('lost', {}).get('pnl', {}).get('average', 0)
            profit_factor = abs(avg_win * winning_trades / max(abs(avg_loss * (total_trades - winning_trades)), 1)) if avg_loss != 0 else 0
            
            # Store results
            results[strategy_name] = {
                'final_value': final_value,
                'total_return': total_return,
                'sharpe_ratio': sharpe_ratio or 0,
                'max_drawdown': drawdown.get('max', {}).get('drawdown', 0),
                'total_trades': total_trades,
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                'sqn': sqn or 0,
            }
            
            # Print results
            logger.info(f"Final Value: ${final_value:,.2f}")
            logger.info(f"Total Return: {total_return:.2f}%")
            logger.info(f"Sharpe Ratio: {sharpe_ratio:.3f}")
            logger.info(f"Max Drawdown: {drawdown.get('max', {}).get('drawdown', 0):.2f}%")
            logger.info(f"Total Trades: {total_trades}")
            logger.info(f"Win Rate: {win_rate:.1f}%")
            logger.info(f"Profit Factor: {profit_factor:.2f}")
            logger.info(f"SQN: {sqn:.2f}")
            
        except Exception as e:
            logger.error(f"Error running {strategy_name}: {e}")
            continue
    
    # Comparison analysis
    if results:
        logger.info(f"\n{'='*70}")
        logger.info("IMPROVEMENT ANALYSIS")
        logger.info(f"{'='*70}")
        
        comparison_df = pd.DataFrame(results).T
        comparison_df = comparison_df.round(3)
        
        print("\nStrategy Performance Comparison:")
        print(comparison_df.to_string())
        
        # Calculate improvements
        if 'Original' in results and any('Improved' in k for k in results.keys()):
            original = results['Original']
            improved_strategies = {k: v for k, v in results.items() if 'Improved' in k}
            
            best_improved = max(improved_strategies.keys(), 
                              key=lambda x: improved_strategies[x]['sharpe_ratio'])
            best_improved_results = improved_strategies[best_improved]
            
            logger.info(f"\n📊 IMPROVEMENT SUMMARY:")
            logger.info(f"Best Improved Strategy: {best_improved}")
            
            return_improvement = best_improved_results['total_return'] - original['total_return']
            sharpe_improvement = best_improved_results['sharpe_ratio'] - original['sharpe_ratio']
            win_rate_improvement = best_improved_results['win_rate'] - original['win_rate']
            trade_reduction = original['total_trades'] - best_improved_results['total_trades']
            
            logger.info(f"Return Improvement: {return_improvement:+.2f}% ({best_improved_results['total_return']:.2f}% vs {original['total_return']:.2f}%)")
            logger.info(f"Sharpe Improvement: {sharpe_improvement:+.3f} ({best_improved_results['sharpe_ratio']:.3f} vs {original['sharpe_ratio']:.3f})")
            logger.info(f"Win Rate Improvement: {win_rate_improvement:+.1f}% ({best_improved_results['win_rate']:.1f}% vs {original['win_rate']:.1f}%)")
            logger.info(f"Trade Reduction: {trade_reduction:.0f} trades ({best_improved_results['total_trades']:.0f} vs {original['total_trades']:.0f})")
            
            # Success metrics
            logger.info(f"\n✅ KEY IMPROVEMENTS:")
            if return_improvement > 0:
                logger.info(f"   • Higher returns: +{return_improvement:.2f}%")
            if sharpe_improvement > 0:
                logger.info(f"   • Better risk-adjusted returns: +{sharpe_improvement:.3f} Sharpe")
            if win_rate_improvement > 0:
                logger.info(f"   • Higher win rate: +{win_rate_improvement:.1f}%")
            if trade_reduction > 0:
                logger.info(f"   • Reduced overtrading: -{trade_reduction:.0f} trades")
            
            if best_improved_results['sharpe_ratio'] > 0:
                logger.info(f"   • ✅ Achieved positive risk-adjusted returns!")
            
            # Save results
            comparison_df.to_csv("improved_strategy_comparison.csv")
            logger.info(f"\n💾 Results saved to improved_strategy_comparison.csv")
        
        # Recommendations
        best_overall = max(results.keys(), key=lambda x: results[x]['sharpe_ratio'])
        logger.info(f"\n🏆 BEST STRATEGY: {best_overall}")
        logger.info(f"   Sharpe Ratio: {results[best_overall]['sharpe_ratio']:.3f}")
        logger.info(f"   Total Return: {results[best_overall]['total_return']:.2f}%")
        logger.info(f"   Win Rate: {results[best_overall]['win_rate']:.1f}%")
        logger.info(f"   Total Trades: {results[best_overall]['total_trades']:.0f}")
    
    else:
        logger.error("No successful backtest results to compare")


if __name__ == "__main__":
    try:
        test_improved_vs_original()
    except KeyboardInterrupt:
        logger.info("\nTest interrupted by user")
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise
