# Backtrader Trading System

A comprehensive Python framework for algorithmic trading and backtesting built on top of the Backtrader library. This system provides a modular, extensible architecture for developing, testing, and optimizing trading strategies.

## Features

- **Modular Architecture**: Clean separation of concerns with dedicated packages for strategies, indicators, data handling, and utilities
- **Multiple Trading Strategies**: Pre-built strategies including SMA, RSI, MACD, and more
- **Custom Indicators**: Extended indicator library with advanced technical analysis tools
- **Configuration Management**: YAML-based configuration with type safety using dataclasses
- **Advanced Logging**: Structured logging with rotation and compression using Loguru
- **Performance Analysis**: Comprehensive performance metrics and reporting
- **Parameter Optimization**: Built-in optimization framework for strategy parameters
- **Data Sources**: Support for Yahoo Finance, CSV files, and extensible data feeds
- **Risk Management**: Built-in stop-loss and take-profit functionality

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd backtrader-trading-system
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Install the package in development mode:
```bash
pip install -e .
```

## Quick Start

### Basic Backtest

```python
from src.main import run_backtest

# Run a simple SMA crossover strategy
results = run_backtest(
    strategy_name="SMAStrategy",
    symbols=["AAPL"],
    start_date="2022-01-01",
    end_date="2023-12-31",
    fast_period=10,
    slow_period=30,
    plot=True
)
```

### Command Line Usage

```bash
# Run backtest from command line
python -m src.main --strategy SMAStrategy --symbols AAPL MSFT --start-date 2022-01-01 --end-date 2023-12-31 --plot

# Optimize strategy parameters
python -m src.optimize --strategy RSIStrategy --symbols AAPL --objective sharpe_ratio
```

### Configuration

The system uses YAML configuration files for easy customization:

```yaml
# config.yaml
data:
  source: "yahoo"
  symbols: ["AAPL", "MSFT", "GOOGL"]
  start_date: "2020-01-01"
  end_date: "2023-12-31"

broker:
  cash: 100000.0
  commission: 0.001

strategy:
  name: "SMAStrategy"
  parameters:
    fast_period: 10
    slow_period: 30
    stop_loss: 0.02
    take_profit: 0.06
```

## Available Strategies

### Moving Average Strategies
- **SMAStrategy**: Simple Moving Average crossover
- **SMATrendStrategy**: Trend following using single SMA
- **SMAMeanReversionStrategy**: Mean reversion based on SMA deviation

### RSI Strategies
- **RSIStrategy**: Classic RSI overbought/oversold
- **RSIDivergenceStrategy**: RSI divergence detection
- **RSIMeanReversionStrategy**: RSI mean reversion with momentum

### MACD Strategies
- **MACDStrategy**: MACD line and signal crossover
- **MACDHistogramStrategy**: MACD histogram momentum
- **MACDDivergenceStrategy**: MACD divergence detection
- **MACDZeroLineStrategy**: MACD zero line crossover

## Custom Indicators

The system includes advanced custom indicators:

- **BollingerBandsPercent**: %B indicator for Bollinger Bands
- **StochasticRSI**: Stochastic applied to RSI
- **WilliamsR**: Williams %R momentum oscillator
- **CommodityChannelIndex**: CCI for cyclical trends
- **AverageDirectionalIndex**: ADX for trend strength
- **ParabolicSAR**: SAR for trend following
- **IchimokuCloud**: Complete Ichimoku system
- **VolumeWeightedAveragePrice**: VWAP calculation
- **MoneyFlowIndex**: Volume-weighted RSI
- **ChaikinOscillator**: A/D line momentum

## Creating Custom Strategies

```python
from src.strategies.base_strategy import BaseStrategy
import backtrader as bt

class MyCustomStrategy(BaseStrategy):
    params = (
        ('my_parameter', 20),
    )
    
    def init_indicators(self):
        self.my_indicator = bt.indicators.SMA(period=self.params.my_parameter)
    
    def next_signal(self):
        if self.dataclose[0] > self.my_indicator[0]:
            return 1  # Buy signal
        elif self.dataclose[0] < self.my_indicator[0]:
            return -1  # Sell signal
        return 0  # No signal
```

## Performance Analysis

The system provides comprehensive performance metrics:

- **Returns**: Total and annualized returns
- **Risk Metrics**: Sharpe ratio, Sortino ratio, Calmar ratio
- **Drawdown Analysis**: Maximum drawdown and duration
- **Risk Measures**: Value at Risk (VaR), Conditional VaR
- **Trade Analysis**: Win rate, profit factor, trade statistics

## Optimization

Optimize strategy parameters using the built-in optimization framework:

```python
from src.optimize import run_optimization

optimize_params = {
    'fast_period': {'start': 5, 'stop': 20, 'step': 5},
    'slow_period': {'start': 20, 'stop': 50, 'step': 10}
}

results = run_optimization(
    strategy_name="SMAStrategy",
    symbols=["AAPL"],
    optimize_params=optimize_params,
    objective='sharpe_ratio'
)
```

## Project Structure

```
backtrader-trading-system/
├── src/
│   ├── __init__.py
│   ├── config.py              # Configuration management
│   ├── main.py               # Main execution script
│   ├── optimize.py           # Optimization framework
│   ├── strategies/           # Trading strategies
│   │   ├── __init__.py
│   │   ├── base_strategy.py
│   │   ├── sma_strategy.py
│   │   ├── rsi_strategy.py
│   │   └── macd_strategy.py
│   ├── indicators/           # Custom indicators
│   │   ├── __init__.py
│   │   └── custom_indicators.py
│   └── utils/               # Utility modules
│       ├── __init__.py
│       ├── data_utils.py
│       ├── logger.py
│       └── performance.py
├── examples/                # Example scripts
├── tests/                   # Test suite
├── data/                    # Data directory
├── logs/                    # Log files
├── config.yaml             # Configuration file
├── requirements.txt        # Dependencies
├── setup.py               # Package setup
└── README.md              # This file
```

## Testing

Run the test suite:

```bash
# Run all tests
python -m pytest tests/

# Run specific test file
python -m pytest tests/test_strategies.py

# Run with coverage
python -m pytest tests/ --cov=src
```

## Examples

Check the `examples/` directory for comprehensive usage examples:

- `basic_backtest.py`: Basic backtesting examples
- `optimization_example.py`: Parameter optimization examples
- `custom_strategy_example.py`: Creating custom strategies

## Logging

The system uses structured logging with automatic rotation:

```python
from src.utils.logger import get_logger

logger = get_logger("my_module")
logger.info("This is an info message")
logger.error("This is an error message")
```

Logs are automatically rotated and compressed, with separate log files for different components.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This software is for educational and research purposes only. Past performance does not guarantee future results. Always do your own research and consider consulting with a financial advisor before making investment decisions.

## Support

For questions, issues, or contributions, please open an issue on the GitHub repository.
