"""
Tests for utility modules.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
import numpy as np
import tempfile
import os

from src.utils.data_utils import DataUtils
from src.utils.performance import PerformanceAnalyzer
from src.utils.logger import get_logger, LoggerMixin


class TestDataUtils(unittest.TestCase):
    """Test data utilities."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.data_utils = DataUtils()
    
    @patch('src.utils.data_utils.yf.download')
    def test_download_yahoo_data(self, mock_download):
        """Test Yahoo Finance data download."""
        # Create mock data
        dates = pd.date_range('2023-01-01', periods=10, freq='D')
        mock_data = pd.DataFrame({
            'Open': range(100, 110),
            'High': range(101, 111),
            'Low': range(99, 109),
            'Close': range(100, 110),
            'Volume': [1000] * 10,
            'Adj Close': range(100, 110)
        }, index=dates)
        
        mock_download.return_value = mock_data
        
        # Test download
        result = self.data_utils.download_yahoo_data(
            symbol="AAPL",
            start_date="2023-01-01",
            end_date="2023-01-10"
        )
        
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), 10)
        self.assertIn('Close', result.columns)
        
        # Verify yfinance was called correctly
        mock_download.assert_called_once_with(
            "AAPL",
            start="2023-01-01",
            end="2023-01-10"
        )
    
    def test_load_csv_data(self):
        """Test CSV data loading."""
        # Create temporary CSV file
        test_data = pd.DataFrame({
            'Date': pd.date_range('2023-01-01', periods=5),
            'Open': [100, 101, 102, 103, 104],
            'High': [101, 102, 103, 104, 105],
            'Low': [99, 100, 101, 102, 103],
            'Close': [100.5, 101.5, 102.5, 103.5, 104.5],
            'Volume': [1000, 1100, 1200, 1300, 1400]
        })
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            test_data.to_csv(f.name, index=False)
            temp_csv_path = f.name
        
        try:
            result = self.data_utils.load_csv_data(temp_csv_path)
            
            self.assertIsInstance(result, pd.DataFrame)
            self.assertEqual(len(result), 5)
            self.assertIn('Close', result.columns)
            
        finally:
            os.unlink(temp_csv_path)
    
    def test_validate_data(self):
        """Test data validation."""
        # Valid data
        valid_data = pd.DataFrame({
            'Open': [100, 101, 102],
            'High': [101, 102, 103],
            'Low': [99, 100, 101],
            'Close': [100.5, 101.5, 102.5],
            'Volume': [1000, 1100, 1200]
        })
        
        self.assertTrue(self.data_utils.validate_data(valid_data))
        
        # Invalid data (missing column)
        invalid_data = pd.DataFrame({
            'Open': [100, 101, 102],
            'High': [101, 102, 103],
            'Low': [99, 100, 101],
            'Volume': [1000, 1100, 1200]
        })
        
        self.assertFalse(self.data_utils.validate_data(invalid_data))
        
        # Invalid data (negative values)
        invalid_data2 = pd.DataFrame({
            'Open': [100, 101, 102],
            'High': [101, 102, 103],
            'Low': [99, 100, 101],
            'Close': [100.5, 101.5, 102.5],
            'Volume': [-1000, 1100, 1200]
        })
        
        self.assertFalse(self.data_utils.validate_data(invalid_data2))


class TestPerformanceAnalyzer(unittest.TestCase):
    """Test performance analysis utilities."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create sample returns data
        np.random.seed(42)
        self.returns = pd.Series(np.random.normal(0.001, 0.02, 252))  # Daily returns for 1 year
        self.returns.index = pd.date_range('2023-01-01', periods=252, freq='D')
        
        self.analyzer = PerformanceAnalyzer()
    
    def test_calculate_sharpe_ratio(self):
        """Test Sharpe ratio calculation."""
        sharpe = self.analyzer.calculate_sharpe_ratio(self.returns)
        
        self.assertIsInstance(sharpe, float)
        self.assertGreater(sharpe, -5)  # Reasonable bounds
        self.assertLess(sharpe, 5)
    
    def test_calculate_sortino_ratio(self):
        """Test Sortino ratio calculation."""
        sortino = self.analyzer.calculate_sortino_ratio(self.returns)
        
        self.assertIsInstance(sortino, float)
        self.assertGreater(sortino, -5)  # Reasonable bounds
        self.assertLess(sortino, 5)
    
    def test_calculate_max_drawdown(self):
        """Test maximum drawdown calculation."""
        # Create cumulative returns
        cumulative_returns = (1 + self.returns).cumprod()
        
        max_dd = self.analyzer.calculate_max_drawdown(cumulative_returns)
        
        self.assertIsInstance(max_dd, float)
        self.assertGreaterEqual(max_dd, 0)  # Drawdown should be positive
        self.assertLessEqual(max_dd, 1)     # Should not exceed 100%
    
    def test_calculate_calmar_ratio(self):
        """Test Calmar ratio calculation."""
        calmar = self.analyzer.calculate_calmar_ratio(self.returns)
        
        self.assertIsInstance(calmar, float)
    
    def test_calculate_var(self):
        """Test Value at Risk calculation."""
        var_95 = self.analyzer.calculate_var(self.returns, confidence_level=0.95)
        var_99 = self.analyzer.calculate_var(self.returns, confidence_level=0.99)
        
        self.assertIsInstance(var_95, float)
        self.assertIsInstance(var_99, float)
        self.assertLess(var_99, var_95)  # 99% VaR should be more extreme
    
    def test_calculate_cvar(self):
        """Test Conditional Value at Risk calculation."""
        cvar = self.analyzer.calculate_cvar(self.returns)
        
        self.assertIsInstance(cvar, float)
        self.assertLess(cvar, 0)  # CVaR should be negative for losses
    
    def test_generate_performance_report(self):
        """Test performance report generation."""
        report = self.analyzer.generate_performance_report(self.returns)
        
        self.assertIsInstance(report, dict)
        
        # Check that all expected metrics are present
        expected_metrics = [
            'total_return', 'annualized_return', 'volatility',
            'sharpe_ratio', 'sortino_ratio', 'calmar_ratio',
            'max_drawdown', 'var_95', 'cvar_95'
        ]
        
        for metric in expected_metrics:
            self.assertIn(metric, report)
            self.assertIsInstance(report[metric], (int, float))


class TestLogger(unittest.TestCase):
    """Test logging utilities."""
    
    def test_get_logger(self):
        """Test logger creation."""
        logger = get_logger("test_logger")
        
        self.assertIsNotNone(logger)
        self.assertEqual(logger.name, "test_logger")
    
    def test_logger_mixin(self):
        """Test LoggerMixin functionality."""
        class TestClass(LoggerMixin):
            def __init__(self):
                super().__init__()
        
        test_obj = TestClass()
        
        self.assertIsNotNone(test_obj.logger)
        self.assertEqual(test_obj.logger.name, "TestClass")


if __name__ == '__main__':
    unittest.main()
