"""
Tests for configuration management.
"""

import unittest
import tempfile
import os
from pathlib import Path

from src.config import Config, DataConfig, <PERSON><PERSON>r<PERSON>onfig, StrategyConfig


class TestConfig(unittest.TestCase):
    """Test configuration management."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_config_content = """
data:
  source: "yahoo"
  symbols: ["AAPL", "MSFT"]
  start_date: "2022-01-01"
  end_date: "2023-12-31"

broker:
  cash: 100000.0
  commission: 0.001

strategy:
  name: "SMAStrategy"
  parameters:
    fast_period: 10
    slow_period: 30
"""
    
    def test_default_config(self):
        """Test default configuration loading."""
        config = Config()
        
        # Test data config
        self.assertIsInstance(config.data, DataConfig)
        self.assertEqual(config.data.source, "yahoo")
        self.assertIn("AAPL", config.data.symbols)
        
        # Test broker config
        self.assertIsInstance(config.broker, BrokerConfig)
        self.assertEqual(config.broker.cash, 100000.0)
        
        # Test strategy config
        self.assertIsInstance(config.strategy, StrategyConfig)
        self.assertEqual(config.strategy.name, "SMAStrategy")
    
    def test_custom_config_file(self):
        """Test loading custom configuration file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(self.test_config_content)
            temp_config_path = f.name
        
        try:
            config = Config(temp_config_path)
            
            # Test loaded values
            self.assertEqual(config.data.symbols, ["AAPL", "MSFT"])
            self.assertEqual(config.broker.cash, 100000.0)
            self.assertEqual(config.strategy.name, "SMAStrategy")
            self.assertEqual(config.strategy.parameters["fast_period"], 10)
            
        finally:
            os.unlink(temp_config_path)
    
    def test_config_update(self):
        """Test configuration updates."""
        config = Config()
        
        # Update data config
        config.data.symbols = ["GOOGL", "TSLA"]
        config.data.start_date = "2021-01-01"
        
        self.assertEqual(config.data.symbols, ["GOOGL", "TSLA"])
        self.assertEqual(config.data.start_date, "2021-01-01")
    
    def test_config_save(self):
        """Test configuration saving."""
        config = Config()
        config.data.symbols = ["TEST"]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            temp_config_path = f.name
        
        try:
            config.save(temp_config_path)
            
            # Load saved config
            saved_config = Config(temp_config_path)
            self.assertEqual(saved_config.data.symbols, ["TEST"])
            
        finally:
            os.unlink(temp_config_path)
    
    def test_invalid_config_file(self):
        """Test handling of invalid configuration file."""
        with self.assertRaises(FileNotFoundError):
            Config("nonexistent_file.yaml")


if __name__ == '__main__':
    unittest.main()
