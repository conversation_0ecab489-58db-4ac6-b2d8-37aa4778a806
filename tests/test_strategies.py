"""
Tests for trading strategies.
"""

import unittest
from unittest.mock import Mock, patch
import backtrader as bt
import pandas as pd

from src.strategies.base_strategy import BaseStrategy
from src.strategies.sma_strategy import SMAStrategy
from src.strategies.rsi_strategy import RSIStrategy
from src.strategies.macd_strategy import MACDStrategy


class MockStrategy(BaseStrategy):
    """Mock strategy for testing BaseStrategy."""
    
    def init_indicators(self):
        """Mock indicator initialization."""
        pass
    
    def next_signal(self):
        """Mock signal generation."""
        return 0


class TestBaseStrategy(unittest.TestCase):
    """Test base strategy functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create mock data
        self.mock_data = Mock()
        self.mock_data.close = [100.0, 101.0, 102.0, 101.5, 100.5]
        self.mock_data.open = [99.5, 100.5, 101.5, 102.0, 101.0]
        self.mock_data.high = [101.0, 102.0, 103.0, 102.5, 101.5]
        self.mock_data.low = [99.0, 100.0, 101.0, 101.0, 100.0]
        self.mock_data.volume = [1000, 1100, 1200, 1050, 950]
    
    def test_strategy_initialization(self):
        """Test strategy initialization."""
        strategy = MockStrategy()
        
        # Test that logger is set up
        self.assertIsNotNone(strategy.logger)
        
        # Test initial values
        self.assertIsNone(strategy.order)
        self.assertIsNone(strategy.buyprice)
        self.assertIsNone(strategy.buycomm)
        self.assertEqual(strategy.trade_count, 0)
        self.assertEqual(strategy.win_count, 0)
        self.assertEqual(strategy.loss_count, 0)
    
    def test_calculate_position_size(self):
        """Test position size calculation."""
        strategy = MockStrategy()
        
        # Mock broker
        strategy.broker = Mock()
        strategy.broker.getvalue.return_value = 100000.0
        strategy.dataclose = [100.0]
        
        # Test position size calculation
        position_size = strategy.calculate_position_size()
        expected_size = int(100000.0 * 0.1 / 100.0)  # 10% of portfolio / price
        self.assertEqual(position_size, max(expected_size, 1))
    
    def test_stop_loss_check(self):
        """Test stop loss functionality."""
        strategy = MockStrategy()
        strategy.buyprice = 100.0
        strategy.dataclose = [98.0]  # 2% loss
        
        # Mock position
        strategy.position = Mock()
        strategy.position.size = 100  # Long position
        
        # Test stop loss trigger
        self.assertTrue(strategy.check_stop_loss())
        
        # Test no stop loss
        strategy.dataclose = [99.5]  # 0.5% loss
        self.assertFalse(strategy.check_stop_loss())
    
    def test_take_profit_check(self):
        """Test take profit functionality."""
        strategy = MockStrategy()
        strategy.buyprice = 100.0
        strategy.dataclose = [106.0]  # 6% profit
        
        # Mock position
        strategy.position = Mock()
        strategy.position.size = 100  # Long position
        
        # Test take profit trigger
        self.assertTrue(strategy.check_take_profit())
        
        # Test no take profit
        strategy.dataclose = [105.0]  # 5% profit
        self.assertFalse(strategy.check_take_profit())


class TestSMAStrategy(unittest.TestCase):
    """Test SMA strategy."""
    
    def test_sma_strategy_signals(self):
        """Test SMA strategy signal generation."""
        # This would require more complex setup with actual backtrader data
        # For now, test that the strategy can be instantiated
        strategy = SMAStrategy()
        self.assertEqual(strategy.params.fast_period, 10)
        self.assertEqual(strategy.params.slow_period, 30)


class TestRSIStrategy(unittest.TestCase):
    """Test RSI strategy."""
    
    def test_rsi_strategy_signals(self):
        """Test RSI strategy signal generation."""
        strategy = RSIStrategy()
        self.assertEqual(strategy.params.rsi_period, 14)
        self.assertEqual(strategy.params.rsi_upper, 70)
        self.assertEqual(strategy.params.rsi_lower, 30)


class TestMACDStrategy(unittest.TestCase):
    """Test MACD strategy."""
    
    def test_macd_strategy_signals(self):
        """Test MACD strategy signal generation."""
        strategy = MACDStrategy()
        self.assertEqual(strategy.params.fast_ema, 12)
        self.assertEqual(strategy.params.slow_ema, 26)
        self.assertEqual(strategy.params.signal_ema, 9)


class TestStrategyIntegration(unittest.TestCase):
    """Integration tests for strategies."""
    
    @patch('src.utils.data_utils.yf.download')
    def test_strategy_with_real_data(self, mock_download):
        """Test strategy with mock real data."""
        # Create mock data
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        mock_data = pd.DataFrame({
            'Open': range(100, 200),
            'High': range(101, 201),
            'Low': range(99, 199),
            'Close': range(100, 200),
            'Volume': [1000] * 100,
            'Adj Close': range(100, 200)
        }, index=dates)
        
        mock_download.return_value = mock_data
        
        # Create cerebro and add strategy
        cerebro = bt.Cerebro()
        cerebro.addstrategy(SMAStrategy, fast_period=5, slow_period=10)
        
        # Create data feed
        data = bt.feeds.PandasData(dataname=mock_data)
        cerebro.adddata(data)
        
        # Run backtest
        results = cerebro.run()
        
        # Test that strategy ran without errors
        self.assertEqual(len(results), 1)
        self.assertIsInstance(results[0], SMAStrategy)


if __name__ == '__main__':
    unittest.main()
