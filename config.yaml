# Backtrader Trading System Configuration

# Data Configuration
data:
  # Default data source
  source: "yahoo"  # Options: yahoo, quandl, csv, ib, oanda
  
  # Data parameters
  symbols:
    - "AAPL"
    - "MSFT"
    - "GOOGL"
    - "TSLA"
  
  # Date range for backtesting
  start_date: "2020-01-01"
  end_date: "2023-12-31"
  
  # Data frequency
  timeframe: "1d"  # Options: 1m, 5m, 15m, 30m, 1h, 1d, 1w, 1M
  
  # CSV data settings (if using CSV files)
  csv:
    directory: "data/csv"
    format: "yahoo"  # Options: yahoo, generic, mt5
    
  # Yahoo Finance settings
  yahoo:
    auto_adjust: true
    back_adjust: false
    
# Broker Configuration
broker:
  # Starting cash
  cash: 100000.0
  
  # Commission settings
  commission: 0.001  # 0.1%
  commission_type: "percent"  # Options: percent, fixed
  
  # Margin settings
  margin: null
  mult: 1.0
  
  # Slippage settings
  slip_perc: 0.0
  slip_fixed: 0.0
  slip_open: false
  slip_match: true

# Strategy Configuration
strategy:
  # Default strategy to use
  name: "SMAStrategy"
  
  # Strategy parameters
  parameters:
    # Moving Average periods
    fast_period: 10
    slow_period: 30
    
    # Risk management
    stop_loss: 0.02  # 2%
    take_profit: 0.06  # 6%
    
    # Position sizing
    position_size: 0.1  # 10% of portfolio
    
# Optimization Configuration
optimization:
  # Parameters to optimize
  optimize_params:
    fast_period:
      start: 5
      stop: 20
      step: 1
    slow_period:
      start: 20
      stop: 50
      step: 5
      
  # Optimization criteria
  objective: "sharpe_ratio"  # Options: total_return, sharpe_ratio, max_drawdown
  
  # CPU usage for optimization
  max_cpus: 4

# Plotting Configuration
plotting:
  # Enable/disable plotting
  enabled: true
  
  # Plot style
  style: "candlestick"  # Options: line, candlestick, ohlc
  
  # Plot size
  figsize: [15, 10]
  
  # Volume plotting
  volume: true
  
  # Indicators to plot
  plot_indicators: true

# Logging Configuration
logging:
  # Log level
  level: "INFO"  # Options: DEBUG, INFO, WARNING, ERROR, CRITICAL
  
  # Log format
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}"
  
  # Log file
  file: "logs/trading_system.log"
  
  # Console logging
  console: true

# Risk Management
risk_management:
  # Maximum drawdown allowed
  max_drawdown: 0.15  # 15%
  
  # Maximum position size
  max_position_size: 0.2  # 20% of portfolio
  
  # Maximum number of positions
  max_positions: 5
  
  # Stop loss settings
  use_stop_loss: true
  stop_loss_percent: 0.02  # 2%
  
  # Take profit settings
  use_take_profit: true
  take_profit_percent: 0.06  # 6%

# Analysis Configuration
analysis:
  # Analyzers to include
  analyzers:
    - "SharpeRatio"
    - "DrawDown"
    - "TradeAnalyzer"
    - "Returns"
    - "TimeReturn"
    
  # Benchmark symbol for comparison
  benchmark: "SPY"
  
  # Risk-free rate for Sharpe ratio calculation
  risk_free_rate: 0.02  # 2%

# Live Trading Configuration (Optional)
live_trading:
  # Enable live trading
  enabled: false
  
  # Broker for live trading
  broker: "ib"  # Options: ib, oanda
  
  # Paper trading mode
  paper_trading: true
  
  # Interactive Brokers settings
  ib:
    host: "127.0.0.1"
    port: 7497  # 7497 for TWS, 4001 for IB Gateway
    client_id: 1
    
  # OANDA settings
  oanda:
    environment: "practice"  # Options: practice, live
    access_token: "your_access_token_here"
    account_id: "your_account_id_here"
