"""
Comparison backtest between original and enhanced S&P 500 EMA strategies.
"""

import backtrader as bt
import yfinance as yf
import pandas as pd
from datetime import datetime
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from src.strategies.sp500_ema_strategy import SP500EMAStrategy, SP500EMAStrategyAggressive
from src.strategies.sp500_ema_strategy_enhanced import (
    SP500EMAStrategyEnhanced, 
    SP500EMAStrategyEnhancedConservative,
    SP500EMAStrategyEnhancedAggressive
)
from src.utils.sp500_utils import SP500Utils
from src.utils.logger import get_logger

logger = get_logger(__name__)


def download_comparison_data():
    """Download data for strategy comparison."""
    # Use a focused set of liquid stocks for comparison
    test_tickers = [
        'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'JPM', 'V', 'UNH',
        'JNJ', 'WMT', 'PG', 'HD', 'MA', 'DIS', 'ADBE', 'NFLX', 'CRM', 'PYPL',
        'SPY'  # Include SPY for market regime detection
    ]
    
    start_date = "2022-01-01"
    end_date = "2024-12-31"
    
    logger.info(f"Downloading data for {len(test_tickers)} tickers...")
    
    try:
        data = yf.download(
            test_tickers,
            start=start_date,
            end=end_date,
            group_by='ticker',
            auto_adjust=True,
            progress=False
        )
        
        if data.empty:
            logger.error("No data downloaded")
            return None, None
        
        # Create data feeds
        data_feeds = []
        successful_tickers = []
        
        for ticker in test_tickers:
            try:
                if len(test_tickers) == 1:
                    ticker_data = data.copy()
                else:
                    ticker_data = data[ticker].copy()
                
                # Validate data
                if ticker_data.empty or len(ticker_data) < 100:
                    logger.warning(f"Insufficient data for {ticker}")
                    continue
                
                # Clean data
                ticker_data = ticker_data.dropna()
                
                # Create data feed
                data_feed = bt.feeds.PandasData(
                    dataname=ticker_data,
                    name=ticker,
                    fromdate=ticker_data.index[0],
                    todate=ticker_data.index[-1],
                    openinterest=None
                )
                data_feeds.append(data_feed)
                successful_tickers.append(ticker)
                
            except Exception as e:
                logger.warning(f"Error processing {ticker}: {e}")
                continue
        
        logger.info(f"Successfully created {len(data_feeds)} data feeds")
        return data_feeds, successful_tickers
        
    except Exception as e:
        logger.error(f"Error downloading data: {e}")
        return None, None


def run_strategy_comparison():
    """Run comparison between original and enhanced strategies."""
    
    logger.info("=" * 60)
    logger.info("S&P 500 EMA Strategy Comparison: Original vs Enhanced")
    logger.info("=" * 60)
    
    # Download data
    data_feeds, tickers = download_comparison_data()
    
    if not data_feeds or len(data_feeds) < 10:
        logger.error("Insufficient data for comparison")
        return
    
    logger.info(f"Testing with {len(data_feeds)} tickers: {tickers}")
    
    # Strategy configurations
    strategies = [
        ("Original_Standard", SP500EMAStrategy),
        ("Original_Aggressive", SP500EMAStrategyAggressive),
        ("Enhanced_Standard", SP500EMAStrategyEnhanced),
        ("Enhanced_Conservative", SP500EMAStrategyEnhancedConservative),
        ("Enhanced_Aggressive", SP500EMAStrategyEnhancedAggressive),
    ]
    
    results = {}
    initial_cash = 100000.0
    
    for strategy_name, strategy_class in strategies:
        logger.info(f"\n{'='*50}")
        logger.info(f"Testing {strategy_name}")
        logger.info(f"{'='*50}")
        
        # Create Cerebro instance
        cerebro = bt.Cerebro()
        
        # Set broker parameters
        cerebro.broker.setcash(initial_cash)
        cerebro.broker.setcommission(commission=0.001)  # 0.1% commission
        
        # Add strategy
        cerebro.addstrategy(strategy_class, printlog=False)  # Reduce logging for comparison
        
        # Add data feeds
        for feed in data_feeds:
            cerebro.adddata(feed)
        
        # Add analyzers
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
        cerebro.addanalyzer(bt.analyzers.SQN, _name='sqn')
        
        # Run backtest
        try:
            results_list = cerebro.run()
            result = results_list[0]
            
            # Get final portfolio value
            final_value = cerebro.broker.getvalue()
            total_return = (final_value - initial_cash) / initial_cash * 100
            
            # Extract analyzer results
            sharpe_ratio = result.analyzers.sharpe.get_analysis().get('sharperatio', 0)
            drawdown = result.analyzers.drawdown.get_analysis()
            trades = result.analyzers.trades.get_analysis()
            sqn = result.analyzers.sqn.get_analysis().get('sqn', 0)
            
            # Calculate additional metrics
            total_trades = trades.get('total', {}).get('total', 0)
            winning_trades = trades.get('won', {}).get('total', 0)
            losing_trades = trades.get('lost', {}).get('total', 0)
            win_rate = (winning_trades / max(total_trades, 1)) * 100
            
            avg_win = trades.get('won', {}).get('pnl', {}).get('average', 0)
            avg_loss = trades.get('lost', {}).get('pnl', {}).get('average', 0)
            profit_factor = abs(avg_win * winning_trades / max(abs(avg_loss * losing_trades), 1)) if avg_loss != 0 else 0
            
            # Store results
            results[strategy_name] = {
                'final_value': final_value,
                'total_return': total_return,
                'sharpe_ratio': sharpe_ratio or 0,
                'max_drawdown': drawdown.get('max', {}).get('drawdown', 0),
                'total_trades': total_trades,
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                'sqn': sqn or 0,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
            }
            
            # Print results
            logger.info(f"Final Value: ${final_value:,.2f}")
            logger.info(f"Total Return: {total_return:.2f}%")
            logger.info(f"Sharpe Ratio: {sharpe_ratio:.3f}")
            logger.info(f"Max Drawdown: {drawdown.get('max', {}).get('drawdown', 0):.2f}%")
            logger.info(f"Total Trades: {total_trades}")
            logger.info(f"Win Rate: {win_rate:.1f}%")
            logger.info(f"Profit Factor: {profit_factor:.2f}")
            logger.info(f"SQN: {sqn:.2f}")
            
        except Exception as e:
            logger.error(f"Error running {strategy_name}: {e}")
            continue
    
    # Comparison analysis
    if results:
        logger.info(f"\n{'='*70}")
        logger.info("DETAILED STRATEGY COMPARISON")
        logger.info(f"{'='*70}")
        
        comparison_df = pd.DataFrame(results).T
        comparison_df = comparison_df.round(3)
        
        print("\nDetailed Performance Comparison:")
        print(comparison_df.to_string())
        
        # Key improvements analysis
        logger.info(f"\n{'='*50}")
        logger.info("IMPROVEMENT ANALYSIS")
        logger.info(f"{'='*50}")
        
        # Compare enhanced vs original
        enhanced_strategies = [k for k in results.keys() if 'Enhanced' in k]
        original_strategies = [k for k in results.keys() if 'Original' in k]
        
        if enhanced_strategies and original_strategies:
            best_enhanced = max(enhanced_strategies, key=lambda x: results[x]['total_return'])
            best_original = max(original_strategies, key=lambda x: results[x]['total_return'])
            
            enhanced_return = results[best_enhanced]['total_return']
            original_return = results[best_original]['total_return']
            return_improvement = enhanced_return - original_return
            
            enhanced_sharpe = results[best_enhanced]['sharpe_ratio']
            original_sharpe = results[best_original]['sharpe_ratio']
            sharpe_improvement = enhanced_sharpe - original_sharpe
            
            enhanced_win_rate = results[best_enhanced]['win_rate']
            original_win_rate = results[best_original]['win_rate']
            win_rate_improvement = enhanced_win_rate - original_win_rate
            
            logger.info(f"Best Enhanced Strategy: {best_enhanced}")
            logger.info(f"Best Original Strategy: {best_original}")
            logger.info(f"")
            logger.info(f"Return Improvement: {return_improvement:+.2f}% ({enhanced_return:.2f}% vs {original_return:.2f}%)")
            logger.info(f"Sharpe Improvement: {sharpe_improvement:+.3f} ({enhanced_sharpe:.3f} vs {original_sharpe:.3f})")
            logger.info(f"Win Rate Improvement: {win_rate_improvement:+.1f}% ({enhanced_win_rate:.1f}% vs {original_win_rate:.1f}%)")
            
            # Key improvements summary
            logger.info(f"\n📈 KEY IMPROVEMENTS:")
            if return_improvement > 0:
                logger.info(f"   ✅ Higher returns: +{return_improvement:.2f}%")
            if sharpe_improvement > 0:
                logger.info(f"   ✅ Better risk-adjusted returns: +{sharpe_improvement:.3f} Sharpe")
            if win_rate_improvement > 0:
                logger.info(f"   ✅ Higher win rate: +{win_rate_improvement:.1f}%")
            
            # Enhanced features working
            logger.info(f"\n🔧 ENHANCED FEATURES:")
            logger.info(f"   • Market regime detection")
            logger.info(f"   • Dynamic position sizing")
            logger.info(f"   • Trailing stops")
            logger.info(f"   • Enhanced signal scoring")
            logger.info(f"   • Volatility filtering")
            logger.info(f"   • Multi-factor trend strength")
        
        # Save results
        comparison_df.to_csv("strategy_comparison_results.csv")
        logger.info(f"\n✅ Comparison results saved to strategy_comparison_results.csv")
        
        # Recommendations
        logger.info(f"\n💡 RECOMMENDATIONS:")
        best_overall = max(results.keys(), key=lambda x: results[x]['sharpe_ratio'])
        logger.info(f"   • Best overall strategy: {best_overall}")
        logger.info(f"   • Sharpe ratio: {results[best_overall]['sharpe_ratio']:.3f}")
        logger.info(f"   • Total return: {results[best_overall]['total_return']:.2f}%")
        
        if results[best_overall]['sharpe_ratio'] > 0:
            logger.info(f"   • ✅ Positive risk-adjusted returns achieved")
        else:
            logger.info(f"   • ⚠️  Still negative Sharpe - consider further optimization")
    
    else:
        logger.error("No successful backtest results to compare")


if __name__ == "__main__":
    try:
        run_strategy_comparison()
    except KeyboardInterrupt:
        logger.info("\nComparison interrupted by user")
    except Exception as e:
        logger.error(f"Comparison failed: {e}")
        raise
