"""
Quick test of the S&P 500 EMA strategy with a small subset of tickers.
"""

import backtrader as bt
import yfinance as yf
import pandas as pd
from datetime import datetime
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from src.strategies.sp500_ema_strategy import SP500EMAStrategy
from src.utils.logger import get_logger

logger = get_logger(__name__)


def test_sp500_strategy():
    """Test the S&P 500 strategy with a small subset of tickers."""
    
    # Test configuration
    test_tickers = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'JPM', 'V', 'UNH']
    start_date = "2023-01-01"
    end_date = "2024-06-30"
    initial_cash = 100000.0
    
    logger.info("=" * 50)
    logger.info("S&P 500 EMA Strategy Test")
    logger.info("=" * 50)
    logger.info(f"Testing with {len(test_tickers)} tickers: {test_tickers}")
    logger.info(f"Period: {start_date} to {end_date}")
    
    # Download data
    logger.info("Downloading test data...")
    try:
        data = yf.download(
            test_tickers,
            start=start_date,
            end=end_date,
            group_by='ticker',
            auto_adjust=True,
            progress=False
        )
        
        if data.empty:
            logger.error("No data downloaded")
            return
        
        logger.info(f"Downloaded data shape: {data.shape}")
        
    except Exception as e:
        logger.error(f"Error downloading data: {e}")
        return
    
    # Create data feeds
    data_feeds = []
    successful_tickers = []
    
    for ticker in test_tickers:
        try:
            if len(test_tickers) == 1:
                ticker_data = data.copy()
            else:
                ticker_data = data[ticker].copy()
            
            # Validate data
            if ticker_data.empty or len(ticker_data) < 50:
                logger.warning(f"Insufficient data for {ticker}")
                continue
            
            # Clean data
            ticker_data = ticker_data.dropna()
            
            # Create data feed
            data_feed = bt.feeds.PandasData(
                dataname=ticker_data,
                name=ticker,
                fromdate=ticker_data.index[0],
                todate=ticker_data.index[-1],
                openinterest=None
            )
            data_feeds.append(data_feed)
            successful_tickers.append(ticker)
            logger.info(f"✓ {ticker}: {len(ticker_data)} days")
            
        except Exception as e:
            logger.warning(f"Error processing {ticker}: {e}")
            continue
    
    if len(data_feeds) < 3:
        logger.error("Need at least 3 data feeds for testing")
        return
    
    logger.info(f"Created {len(data_feeds)} data feeds for: {successful_tickers}")
    
    # Create Cerebro instance
    cerebro = bt.Cerebro()
    
    # Set broker parameters
    cerebro.broker.setcash(initial_cash)
    cerebro.broker.setcommission(commission=0.001)  # 0.1% commission
    
    # Add strategy
    cerebro.addstrategy(SP500EMAStrategy, printlog=True)
    
    # Add data feeds
    for feed in data_feeds:
        cerebro.adddata(feed)
    
    # Add analyzers
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
    
    # Run backtest
    logger.info(f"Starting test backtest...")
    logger.info(f"Initial Portfolio Value: ${initial_cash:,.2f}")
    
    try:
        results = cerebro.run()
        result = results[0]
        
        # Get final portfolio value
        final_value = cerebro.broker.getvalue()
        total_return = (final_value - initial_cash) / initial_cash * 100
        
        # Extract analyzer results
        sharpe_ratio = result.analyzers.sharpe.get_analysis().get('sharperatio', 0)
        drawdown = result.analyzers.drawdown.get_analysis()
        trades = result.analyzers.trades.get_analysis()
        
        # Print results
        logger.info(f"\n{'='*40}")
        logger.info("TEST RESULTS")
        logger.info(f"{'='*40}")
        logger.info(f"Final Portfolio Value: ${final_value:,.2f}")
        logger.info(f"Total Return: {total_return:.2f}%")
        logger.info(f"Sharpe Ratio: {sharpe_ratio:.3f}")
        logger.info(f"Max Drawdown: {drawdown.get('max', {}).get('drawdown', 0):.2f}%")
        logger.info(f"Total Trades: {trades.get('total', {}).get('total', 0)}")
        
        win_rate = 0
        if trades.get('total', {}).get('total', 0) > 0:
            win_rate = (trades.get('won', {}).get('total', 0) / 
                       trades.get('total', {}).get('total', 1)) * 100
        
        logger.info(f"Win Rate: {win_rate:.1f}%")
        
        # Test success criteria
        if total_return > -10 and trades.get('total', {}).get('total', 0) > 0:
            logger.info("\n✅ Test PASSED - Strategy executed successfully")
            logger.info("   • Strategy generated trades")
            logger.info("   • No major losses detected")
            logger.info("   • Multi-asset signal selection working")
        else:
            logger.warning("\n⚠️  Test completed with concerns")
            logger.warning("   • Check strategy parameters")
            logger.warning("   • Verify signal generation logic")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise


if __name__ == "__main__":
    test_sp500_strategy()
