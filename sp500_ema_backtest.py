"""
S&P 500 EMA Multi-Asset Strategy Backtest.

This script runs a backtest of the S&P 500 EMA strategy that selects
the best signal from all S&P 500 stocks for trading.
"""

import backtrader as bt
import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
import sys
import os
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from src.strategies.sp500_ema_strategy import (
    SP500EMAStrategy, 
    SP500EMAStrategyConservative, 
    SP500EMAStrategyAggressive
)
from src.utils.sp500_utils import SP500Utils
from src.utils.logger import get_logger

logger = get_logger(__name__)


def download_sp500_data(tickers, start_date, end_date, max_tickers=50):
    """Download data for S&P 500 tickers with error handling."""
    logger.info(f"Downloading data for {len(tickers)} tickers from {start_date} to {end_date}")
    
    successful_data = {}
    failed_tickers = []
    
    # Process in smaller batches to avoid API limits
    batch_size = 10
    for i in range(0, min(len(tickers), max_tickers), batch_size):
        batch = tickers[i:i + batch_size]
        logger.info(f"Processing batch {i//batch_size + 1}: {batch}")
        
        try:
            # Download batch data
            data = yf.download(
                batch,
                start=start_date,
                end=end_date,
                group_by='ticker',
                auto_adjust=True,
                prepost=True,
                threads=True,
                progress=False
            )
            
            if data.empty:
                logger.warning(f"No data returned for batch: {batch}")
                failed_tickers.extend(batch)
                continue
            
            # Process each ticker in the batch
            for ticker in batch:
                try:
                    if len(batch) == 1:
                        # Single ticker case
                        ticker_data = data.copy()
                    else:
                        # Multi-ticker case
                        if ticker in data.columns.levels[0]:
                            ticker_data = data[ticker].copy()
                        else:
                            logger.warning(f"No data found for {ticker}")
                            failed_tickers.append(ticker)
                            continue
                    
                    # Validate data quality
                    if ticker_data.empty or len(ticker_data) < 100:  # Need at least 100 days
                        logger.warning(f"Insufficient data for {ticker}: {len(ticker_data)} days")
                        failed_tickers.append(ticker)
                        continue
                    
                    # Clean data
                    ticker_data = ticker_data.dropna()
                    
                    # Ensure required columns exist
                    required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
                    if not all(col in ticker_data.columns for col in required_cols):
                        logger.warning(f"Missing required columns for {ticker}")
                        failed_tickers.append(ticker)
                        continue
                    
                    # Store successful data
                    successful_data[ticker] = ticker_data
                    logger.info(f"✓ {ticker}: {len(ticker_data)} days of data")
                
                except Exception as e:
                    logger.error(f"Error processing {ticker}: {e}")
                    failed_tickers.append(ticker)
                    continue
        
        except Exception as e:
            logger.error(f"Error downloading batch {batch}: {e}")
            failed_tickers.extend(batch)
            continue
    
    logger.info(f"Successfully downloaded data for {len(successful_data)} tickers")
    if failed_tickers:
        logger.warning(f"Failed to download data for {len(failed_tickers)} tickers: {failed_tickers[:10]}...")
    
    return successful_data


def create_data_feeds(data_dict):
    """Create Backtrader data feeds from downloaded data."""
    data_feeds = []
    
    for ticker, data in data_dict.items():
        try:
            # Create Backtrader data feed
            data_feed = bt.feeds.PandasData(
                dataname=data,
                name=ticker,
                fromdate=data.index[0],
                todate=data.index[-1],
                openinterest=None
            )
            data_feeds.append(data_feed)
            
        except Exception as e:
            logger.error(f"Error creating data feed for {ticker}: {e}")
            continue
    
    logger.info(f"Created {len(data_feeds)} data feeds")
    return data_feeds


def run_sp500_ema_backtest():
    """Run the S&P 500 EMA strategy backtest."""
    
    # Configuration
    start_date = "2022-01-01"
    end_date = "2024-12-31"
    initial_cash = 100000.0
    max_tickers = 100  # Limit for testing (use fewer for faster execution)
    
    logger.info("=" * 60)
    logger.info("S&P 500 EMA Multi-Asset Strategy Backtest")
    logger.info("=" * 60)
    
    # Get S&P 500 tickers
    sp500_utils = SP500Utils()
    all_tickers = sp500_utils.get_sp500_tickers()
    
    # Filter for liquid stocks
    liquid_tickers = sp500_utils.filter_by_liquidity(all_tickers[:max_tickers], min_volume=500000)
    
    if len(liquid_tickers) < 10:
        logger.warning("Too few liquid tickers found, using fallback list")
        liquid_tickers = sp500_utils._get_fallback_tickers()[:max_tickers]
    
    logger.info(f"Using {len(liquid_tickers)} liquid S&P 500 tickers")
    
    # Download data
    data_dict = download_sp500_data(liquid_tickers, start_date, end_date, max_tickers)
    
    if len(data_dict) < 5:
        logger.error("Insufficient data downloaded. Cannot proceed with backtest.")
        return
    
    # Create data feeds
    data_feeds = create_data_feeds(data_dict)
    
    if len(data_feeds) < 5:
        logger.error("Insufficient data feeds created. Cannot proceed with backtest.")
        return
    
    # Strategy configurations
    strategies = [
        ("SP500_EMA_Standard", SP500EMAStrategy),
        ("SP500_EMA_Conservative", SP500EMAStrategyConservative),
        ("SP500_EMA_Aggressive", SP500EMAStrategyAggressive),
    ]
    
    results = {}
    
    for strategy_name, strategy_class in strategies:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running {strategy_name}")
        logger.info(f"{'='*50}")
        
        # Create Cerebro instance
        cerebro = bt.Cerebro()
        
        # Set broker parameters
        cerebro.broker.setcash(initial_cash)
        cerebro.broker.setcommission(commission=0.001)  # 0.1% commission
        
        # Add strategy
        cerebro.addstrategy(strategy_class, printlog=True)
        
        # Add data feeds
        for feed in data_feeds:
            cerebro.adddata(feed)
        
        # Add analyzers
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
        
        # Run backtest
        logger.info(f"Starting backtest with {len(data_feeds)} data feeds...")
        logger.info(f"Initial Portfolio Value: ${initial_cash:,.2f}")
        
        try:
            results_list = cerebro.run()
            result = results_list[0]
            
            # Get final portfolio value
            final_value = cerebro.broker.getvalue()
            total_return = (final_value - initial_cash) / initial_cash * 100
            
            # Extract analyzer results
            sharpe_ratio = result.analyzers.sharpe.get_analysis().get('sharperatio', 0)
            drawdown = result.analyzers.drawdown.get_analysis()
            trades = result.analyzers.trades.get_analysis()
            
            # Store results
            results[strategy_name] = {
                'final_value': final_value,
                'total_return': total_return,
                'sharpe_ratio': sharpe_ratio or 0,
                'max_drawdown': drawdown.get('max', {}).get('drawdown', 0),
                'total_trades': trades.get('total', {}).get('total', 0),
                'winning_trades': trades.get('won', {}).get('total', 0),
                'losing_trades': trades.get('lost', {}).get('total', 0),
                'win_rate': (trades.get('won', {}).get('total', 0) / 
                           max(trades.get('total', {}).get('total', 1), 1)) * 100
            }
            
            # Print results
            logger.info(f"\n{strategy_name} Results:")
            logger.info(f"Final Portfolio Value: ${final_value:,.2f}")
            logger.info(f"Total Return: {total_return:.2f}%")
            logger.info(f"Sharpe Ratio: {sharpe_ratio:.3f}")
            logger.info(f"Max Drawdown: {drawdown.get('max', {}).get('drawdown', 0):.2f}%")
            logger.info(f"Total Trades: {trades.get('total', {}).get('total', 0)}")
            logger.info(f"Win Rate: {results[strategy_name]['win_rate']:.1f}%")
            
        except Exception as e:
            logger.error(f"Error running {strategy_name}: {e}")
            continue
    
    # Summary comparison
    if results:
        logger.info(f"\n{'='*60}")
        logger.info("STRATEGY COMPARISON SUMMARY")
        logger.info(f"{'='*60}")
        
        comparison_df = pd.DataFrame(results).T
        comparison_df = comparison_df.round(2)
        
        print("\nStrategy Performance Comparison:")
        print(comparison_df.to_string())
        
        # Save results
        comparison_df.to_csv("sp500_ema_strategy_results.csv")
        logger.info("\n✅ Results saved to sp500_ema_strategy_results.csv")
        
        # Find best strategy
        best_strategy = comparison_df['total_return'].idxmax()
        best_return = comparison_df.loc[best_strategy, 'total_return']
        best_sharpe = comparison_df.loc[best_strategy, 'sharpe_ratio']
        
        logger.info(f"\n🏆 Best Strategy: {best_strategy}")
        logger.info(f"   Return: {best_return:.2f}%")
        logger.info(f"   Sharpe Ratio: {best_sharpe:.3f}")
        
        # Performance insights
        logger.info(f"\n📊 Performance Insights:")
        logger.info(f"   • Tested {len(data_feeds)} S&P 500 stocks")
        logger.info(f"   • Period: {start_date} to {end_date}")
        logger.info(f"   • Multi-asset signal selection approach")
        logger.info(f"   • Best signal scoring and ranking system")
    
    else:
        logger.error("No successful backtest results to display")


if __name__ == "__main__":
    try:
        run_sp500_ema_backtest()
    except KeyboardInterrupt:
        logger.info("\nBacktest interrupted by user")
    except Exception as e:
        logger.error(f"Backtest failed: {e}")
        raise
