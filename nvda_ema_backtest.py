#!/usr/bin/env python3
"""
NVDA EMA Strategy Backtest Script.

This script runs a 3-year backtest of the NVDA EMA strategy.
"""

import sys
import os
from datetime import datetime, timedelta
import backtrader as bt
import yfinance as yf
import pandas as pd

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.strategies.nvda_ema_strategy import NVDAEMAStrategy
from src.utils.performance import PerformanceAnalyzer
from src.utils.logger import setup_logger


def download_nvda_data(start_date, end_date):
    """Download NVDA data from Yahoo Finance."""
    print(f"Downloading NVDA data from {start_date} to {end_date}...")

    try:
        data = yf.download('NVDA', start=start_date, end=end_date)

        if data.empty:
            raise ValueError("No data downloaded")

        # Flatten column names if they are MultiIndex
        if isinstance(data.columns, pd.MultiIndex):
            data.columns = [col[0] for col in data.columns]

        # Ensure we have the required columns
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in required_columns:
            if col not in data.columns:
                raise ValueError(f"Missing required column: {col}")

        print(f"Downloaded {len(data)} days of data")
        return data

    except Exception as e:
        print(f"Error downloading data: {e}")
        return None


def create_data_feed(data):
    """Create backtrader data feed from pandas DataFrame."""
    # Ensure we have the required columns
    required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
    for col in required_columns:
        if col not in data.columns:
            raise ValueError(f"Missing required column: {col}")
    
    # Create backtrader data feed
    data_feed = bt.feeds.PandasData(
        dataname=data,
        datetime=None,  # Use index as datetime
        open='Open',
        high='High',
        low='Low',
        close='Close',
        volume='Volume',
        openinterest=None
    )
    
    return data_feed


def run_nvda_backtest():
    """Run the NVDA EMA strategy backtest."""
    # Setup logging
    setup_logger()
    
    # Calculate date range (3 years back from today)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3*365)  # Approximately 3 years
    
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')
    
    print(f"Running NVDA EMA Strategy Backtest")
    print(f"Period: {start_date_str} to {end_date_str}")
    print("=" * 60)
    
    # Download data
    data = download_nvda_data(start_date_str, end_date_str)
    if data is None:
        print("Failed to download data. Exiting.")
        return None
    
    # Create Cerebro engine
    cerebro = bt.Cerebro()
    
    # Set broker configuration
    initial_cash = 100000.0
    cerebro.broker.setcash(initial_cash)
    cerebro.broker.setcommission(commission=0.001)  # 0.1% commission
    
    # Add data feed
    data_feed = create_data_feed(data)
    cerebro.adddata(data_feed)
    
    # Add strategy
    cerebro.addstrategy(NVDAEMAStrategy)
    
    # Add analyzers
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.SQN, _name='sqn')
    
    # Print starting conditions
    print(f'Starting Portfolio Value: ${cerebro.broker.getvalue():,.2f}')
    print()
    
    # Run backtest
    print("Running backtest...")
    results = cerebro.run()
    
    # Get strategy results
    strategy = results[0]
    
    # Print ending conditions
    final_value = cerebro.broker.getvalue()
    total_return = (final_value - initial_cash) / initial_cash * 100
    
    print()
    print("=" * 60)
    print("BACKTEST RESULTS")
    print("=" * 60)
    print(f'Starting Portfolio Value: ${initial_cash:,.2f}')
    print(f'Final Portfolio Value: ${final_value:,.2f}')
    print(f'Total Return: {total_return:.2f}%')
    print()
    
    # Print analyzer results
    print("PERFORMANCE METRICS")
    print("-" * 30)
    
    # Sharpe Ratio
    sharpe = strategy.analyzers.sharpe.get_analysis()
    if 'sharperatio' in sharpe and sharpe['sharperatio'] is not None:
        print(f'Sharpe Ratio: {sharpe["sharperatio"]:.3f}')
    else:
        print('Sharpe Ratio: N/A')
    
    # Drawdown
    drawdown = strategy.analyzers.drawdown.get_analysis()
    print(f'Max Drawdown: {drawdown["max"]["drawdown"]:.2f}%')
    print(f'Max Drawdown Duration: {drawdown["max"]["len"]} days')
    
    # Returns
    returns = strategy.analyzers.returns.get_analysis()
    if 'rtot' in returns:
        print(f'Total Return: {returns["rtot"]:.2f}%')
    if 'rnorm' in returns:
        print(f'Normalized Return: {returns["rnorm"]:.2f}%')
    
    # Trade Analysis
    trades = strategy.analyzers.trades.get_analysis()
    if 'total' in trades and 'total' in trades['total']:
        total_trades = trades['total']['total']
        print(f'Total Trades: {total_trades}')
        
        if total_trades > 0:
            won_trades = trades['won']['total'] if 'won' in trades else 0
            lost_trades = trades['lost']['total'] if 'lost' in trades else 0
            win_rate = (won_trades / total_trades) * 100 if total_trades > 0 else 0
            
            print(f'Winning Trades: {won_trades}')
            print(f'Losing Trades: {lost_trades}')
            print(f'Win Rate: {win_rate:.1f}%')
            
            if 'won' in trades and 'pnl' in trades['won']:
                avg_win = trades['won']['pnl']['average']
                print(f'Average Win: ${avg_win:.2f}')
            
            if 'lost' in trades and 'pnl' in trades['lost']:
                avg_loss = trades['lost']['pnl']['average']
                print(f'Average Loss: ${avg_loss:.2f}')
    
    # SQN (System Quality Number)
    sqn = strategy.analyzers.sqn.get_analysis()
    if 'sqn' in sqn and sqn['sqn'] is not None:
        print(f'SQN: {sqn["sqn"]:.3f}')
    
    print()
    print("STRATEGY PARAMETERS")
    print("-" * 30)
    print(f'EMA Periods: {strategy.params.ema_fast}, {strategy.params.ema_medium}, {strategy.params.ema_slow}')
    print(f'Position Size: {strategy.params.position_size * 100:.1f}% of portfolio')
    print(f'Stop Loss: {strategy.params.stop_loss * 100:.1f}%')
    print(f'Take Profit: {strategy.params.take_profit * 100:.1f}%')
    
    # Plot results
    print("\nGenerating plot...")
    try:
        cerebro.plot(style='candlestick', barup='green', bardown='red')
        print("Plot generated successfully!")
    except Exception as e:
        print(f"Error generating plot: {e}")
    
    return {
        'strategy': strategy,
        'final_value': final_value,
        'total_return': total_return,
        'analyzers': {
            'sharpe': sharpe,
            'drawdown': drawdown,
            'returns': returns,
            'trades': trades,
            'sqn': sqn
        }
    }


def run_comparison_backtest():
    """Run comparison between different strategy variants."""
    print("\n" + "=" * 60)
    print("RUNNING STRATEGY COMPARISON")
    print("=" * 60)
    
    # Import strategy variants
    from src.strategies.nvda_ema_strategy import (
        NVDAEMAStrategy, 
        NVDAEMAStrategyConservative, 
        NVDAEMAStrategyAggressive
    )
    
    strategies = [
        ("Standard", NVDAEMAStrategy),
        ("Conservative", NVDAEMAStrategyConservative),
        ("Aggressive", NVDAEMAStrategyAggressive)
    ]
    
    results = {}
    
    for name, strategy_class in strategies:
        print(f"\nRunning {name} strategy...")
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=3*365)
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        # Download data
        data = download_nvda_data(start_date_str, end_date_str)
        if data is None:
            continue
        
        # Create Cerebro
        cerebro = bt.Cerebro()
        cerebro.broker.setcash(100000.0)
        cerebro.broker.setcommission(commission=0.001)
        
        # Add data and strategy
        data_feed = create_data_feed(data)
        cerebro.adddata(data_feed)
        cerebro.addstrategy(strategy_class)
        
        # Add analyzers
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
        
        # Run
        strategy_results = cerebro.run()
        final_value = cerebro.broker.getvalue()
        total_return = (final_value - 100000) / 100000 * 100
        
        results[name] = {
            'final_value': final_value,
            'total_return': total_return,
            'strategy': strategy_results[0]
        }
    
    # Print comparison
    print("\n" + "=" * 60)
    print("STRATEGY COMPARISON RESULTS")
    print("=" * 60)
    print(f"{'Strategy':<15} {'Final Value':<15} {'Total Return':<15} {'Sharpe':<10}")
    print("-" * 60)
    
    for name, result in results.items():
        sharpe = result['strategy'].analyzers.sharpe.get_analysis()
        sharpe_ratio = sharpe.get('sharperatio', 'N/A')
        if sharpe_ratio != 'N/A' and sharpe_ratio is not None:
            sharpe_str = f"{sharpe_ratio:.3f}"
        else:
            sharpe_str = "N/A"
        
        print(f"{name:<15} ${result['final_value']:<14,.0f} {result['total_return']:<14.2f}% {sharpe_str:<10}")


if __name__ == "__main__":
    print("NVDA EMA Strategy Backtest")
    print("=" * 60)
    
    # Run main backtest
    main_results = run_nvda_backtest()
    
    if main_results:
        # Run comparison
        run_comparison_backtest()
        
        print("\n" + "=" * 60)
        print("Backtest completed successfully!")
        print("Check the generated plot for visual analysis.")
        print("=" * 60)
    else:
        print("Backtest failed!")
