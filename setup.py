#!/usr/bin/env python3
"""
Setup script for the Backtrader Trading System
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="backtrader-trading-system",
    version="1.0.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="A comprehensive trading system built with Backtrader",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/backtrader-trading-system",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial :: Investment",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "isort>=5.10.0",
        ],
        "live": [
            "ib-insync>=0.9.70",
            "oandapyV20>=0.6.3",
        ],
        "analytics": [
            "pyfolio>=0.9.2",
            "empyrical>=0.5.5",
        ],
    },
    entry_points={
        "console_scripts": [
            "bt-run=src.main:main",
            "bt-optimize=src.optimize:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.json", "*.csv"],
    },
)
