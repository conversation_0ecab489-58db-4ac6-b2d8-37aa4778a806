#!/usr/bin/env python3
"""
Basic backtest example using the Backtrader Trading System.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.main import run_backtest


def main():
    """Run a basic backtest example."""
    print("Running Basic Backtest Example")
    print("=" * 50)
    
    # Example 1: Simple SMA Strategy
    print("\n1. SMA Crossover Strategy on AAPL")
    results = run_backtest(
        strategy_name="SMAStrategy",
        symbols=["AAPL"],
        start_date="2022-01-01",
        end_date="2023-12-31",
        fast_period=10,
        slow_period=30,
        plot=False
    )
    
    # Example 2: RSI Strategy
    print("\n2. RSI Strategy on Multiple Symbols")
    results = run_backtest(
        strategy_name="RSIStrategy",
        symbols=["MSFT", "GOOGL"],
        start_date="2022-01-01",
        end_date="2023-12-31",
        rsi_period=14,
        rsi_upper=70,
        rsi_lower=30,
        plot=False
    )
    
    # Example 3: MACD Strategy
    print("\n3. MACD Strategy on SPY")
    results = run_backtest(
        strategy_name="MACDStrategy",
        symbols=["SPY"],
        start_date="2022-01-01",
        end_date="2023-12-31",
        fast_ema=12,
        slow_ema=26,
        signal_ema=9,
        plot=False
    )
    
    print("\nBasic backtest examples completed!")


if __name__ == "__main__":
    main()
