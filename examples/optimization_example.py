#!/usr/bin/env python3
"""
Optimization example using the Backtrader Trading System.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.optimize import run_optimization, print_optimization_results


def main():
    """Run optimization examples."""
    print("Running Optimization Examples")
    print("=" * 50)
    
    # Example 1: Optimize SMA Strategy
    print("\n1. Optimizing SMA Strategy Parameters")
    
    # Define optimization parameters
    optimize_params = {
        'fast_period': {'start': 5, 'stop': 20, 'step': 5},
        'slow_period': {'start': 20, 'stop': 50, 'step': 10}
    }
    
    results_df = run_optimization(
        strategy_name="SMAStrategy",
        symbols=["AAPL"],
        start_date="2022-01-01",
        end_date="2023-12-31",
        optimize_params=optimize_params,
        objective='sharpe_ratio',
        max_cpus=1
    )
    
    if results_df is not None:
        print_optimization_results(results_df, top_n=5)
    
    # Example 2: Optimize RSI Strategy
    print("\n2. Optimizing RSI Strategy Parameters")
    
    optimize_params = {
        'rsi_period': {'start': 10, 'stop': 20, 'step': 2},
        'rsi_upper': {'start': 65, 'stop': 80, 'step': 5},
        'rsi_lower': {'start': 20, 'stop': 35, 'step': 5}
    }
    
    results_df = run_optimization(
        strategy_name="RSIStrategy",
        symbols=["MSFT"],
        start_date="2022-01-01",
        end_date="2023-12-31",
        optimize_params=optimize_params,
        objective='total_return',
        max_cpus=1
    )
    
    if results_df is not None:
        print_optimization_results(results_df, top_n=5)
    
    print("\nOptimization examples completed!")


if __name__ == "__main__":
    main()
