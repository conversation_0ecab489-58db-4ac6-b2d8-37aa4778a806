#!/usr/bin/env python3
"""
Custom strategy example using the Backtrader Trading System.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import backtrader as bt
from src.strategies.base_strategy import BaseStrategy
from src.main import run_backtest


class BollingerBandsStrategy(BaseStrategy):
    """Custom Bollinger Bands strategy example.
    
    This strategy uses Bollinger Bands to identify overbought/oversold conditions:
    - Buy when price touches lower band
    - Sell when price touches upper band
    """
    
    params = (
        ('bb_period', 20),
        ('bb_devfactor', 2.0),
        ('printlog', False),
        ('stop_loss', 0.02),
        ('take_profit', 0.06),
        ('position_size', 0.1),
    )
    
    def init_indicators(self):
        """Initialize Bollinger Bands indicator."""
        self.bb = bt.indicators.BollingerBands(
            self.datas[0],
            period=self.params.bb_period,
            devfactor=self.params.bb_devfactor
        )
        
        self.logger.info(f"Bollinger Bands Strategy initialized with "
                        f"period={self.params.bb_period}, "
                        f"devfactor={self.params.bb_devfactor}")
    
    def next_signal(self):
        """Generate trading signals based on Bollinger Bands.
        
        Returns:
            1 for buy signal, -1 for sell signal, 0 for no signal
        """
        # Check for stop loss or take profit first
        if self.position:
            if self.check_stop_loss():
                self.log(f'STOP LOSS triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
            
            if self.check_take_profit():
                self.log(f'TAKE PROFIT triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
        
        # Bollinger Bands signals
        if not self.position:
            # Buy when price touches or goes below lower band
            if self.dataclose[0] <= self.bb.lines.bot[0]:
                return 1
        else:
            # Sell when price touches or goes above upper band
            if self.dataclose[0] >= self.bb.lines.top[0]:
                return -1
        
        return 0
    
    def next(self):
        """Override next to add BB-specific logging."""
        # Log Bollinger Bands values
        self.log(f'BB Top: {self.bb.lines.top[0]:.2f}, '
                f'BB Mid: {self.bb.lines.mid[0]:.2f}, '
                f'BB Bot: {self.bb.lines.bot[0]:.2f}')
        
        # Call parent next method
        super().next()


class MeanReversionStrategy(BaseStrategy):
    """Custom mean reversion strategy example.
    
    This strategy combines multiple indicators for mean reversion:
    - Uses RSI and Bollinger Bands
    - Buy when both indicators show oversold conditions
    - Sell when both indicators show overbought conditions
    """
    
    params = (
        ('rsi_period', 14),
        ('rsi_upper', 70),
        ('rsi_lower', 30),
        ('bb_period', 20),
        ('bb_devfactor', 2.0),
        ('printlog', False),
        ('stop_loss', 0.02),
        ('take_profit', 0.06),
        ('position_size', 0.1),
    )
    
    def init_indicators(self):
        """Initialize RSI and Bollinger Bands indicators."""
        self.rsi = bt.indicators.RSI(
            self.datas[0],
            period=self.params.rsi_period
        )
        
        self.bb = bt.indicators.BollingerBands(
            self.datas[0],
            period=self.params.bb_period,
            devfactor=self.params.bb_devfactor
        )
        
        # Calculate %B (Bollinger Bands Percent)
        self.bb_percent = (
            (self.dataclose - self.bb.lines.bot) / 
            (self.bb.lines.top - self.bb.lines.bot)
        )
        
        self.logger.info(f"Mean Reversion Strategy initialized with "
                        f"rsi_period={self.params.rsi_period}, "
                        f"bb_period={self.params.bb_period}")
    
    def next_signal(self):
        """Generate trading signals based on combined indicators.
        
        Returns:
            1 for buy signal, -1 for sell signal, 0 for no signal
        """
        # Check for stop loss or take profit first
        if self.position:
            if self.check_stop_loss():
                self.log(f'STOP LOSS triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
            
            if self.check_take_profit():
                self.log(f'TAKE PROFIT triggered at {self.dataclose[0]:.2f}')
                return -1 if self.position.size > 0 else 1
        
        # Combined signals
        if not self.position:
            # Buy when both RSI and %B show oversold conditions
            if (self.rsi[0] < self.params.rsi_lower and 
                self.bb_percent[0] < 0.2):  # %B below 20%
                self.log('OVERSOLD condition detected (RSI + BB)')
                return 1
        else:
            # Sell when both RSI and %B show overbought conditions
            if (self.rsi[0] > self.params.rsi_upper and 
                self.bb_percent[0] > 0.8):  # %B above 80%
                self.log('OVERBOUGHT condition detected (RSI + BB)')
                return -1
        
        return 0
    
    def next(self):
        """Override next to add indicator-specific logging."""
        # Log indicator values
        self.log(f'RSI: {self.rsi[0]:.2f}, BB %B: {self.bb_percent[0]:.2f}')
        
        # Call parent next method
        super().next()


def main():
    """Run custom strategy examples."""
    print("Running Custom Strategy Examples")
    print("=" * 50)
    
    # Register custom strategies (in a real application, you'd add these to the strategy registry)
    from src.strategies import STRATEGY_REGISTRY
    STRATEGY_REGISTRY['BollingerBandsStrategy'] = BollingerBandsStrategy
    STRATEGY_REGISTRY['MeanReversionStrategy'] = MeanReversionStrategy
    
    # Example 1: Bollinger Bands Strategy
    print("\n1. Custom Bollinger Bands Strategy")
    results = run_backtest(
        strategy_name="BollingerBandsStrategy",
        symbols=["AAPL"],
        start_date="2022-01-01",
        end_date="2023-12-31",
        bb_period=20,
        bb_devfactor=2.0,
        plot=False
    )
    
    # Example 2: Mean Reversion Strategy
    print("\n2. Custom Mean Reversion Strategy")
    results = run_backtest(
        strategy_name="MeanReversionStrategy",
        symbols=["MSFT"],
        start_date="2022-01-01",
        end_date="2023-12-31",
        rsi_period=14,
        bb_period=20,
        plot=False
    )
    
    print("\nCustom strategy examples completed!")
    print("\nTo create your own custom strategy:")
    print("1. Inherit from BaseStrategy")
    print("2. Implement init_indicators() method")
    print("3. Implement next_signal() method")
    print("4. Optionally override next() for custom logging")
    print("5. Register your strategy in the STRATEGY_REGISTRY")


if __name__ == "__main__":
    main()
